<?php
/**
 * GeneratePress child theme functions and definitions.
 *
 * Add your custom PHP in this file.
 * Only edit this file if you have direct access to it on your server (to fix errors if they happen).
 */

// Enqueue scripts and styles for project filtering
function enqueue_project_filter_assets() {
    // Enqueue the JavaScript file
    wp_enqueue_script(
        'project-filter-js',
        get_stylesheet_directory_uri() . '/js/project-filter.js',
        array('jquery'),
        '1.0.0',
        true
    );

    // Enqueue the CSS file
    wp_enqueue_style(
        'project-filter-css',
        get_stylesheet_directory_uri() . '/css/project-filter.css',
        array(),
        '1.0.0'
    );

    // Localize script for AJAX
    wp_localize_script('project-filter-js', 'project_filter_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('project_filter_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_project_filter_assets');

// AJAX handler for logged-in users
add_action('wp_ajax_filter_projects', 'handle_project_filter_ajax');
// AJAX handler for non-logged-in users
add_action('wp_ajax_nopriv_filter_projects', 'handle_project_filter_ajax');

function handle_project_filter_ajax() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'project_filter_nonce')) {
        wp_die('Security check failed');
    }

    // Get filter parameters
    $project_year = sanitize_text_field($_POST['project_year'] ?? '');
    $industry_category = sanitize_text_field($_POST['industry_category'] ?? '');
    $paged = intval($_POST['paged'] ?? 1);
    $posts_per_page = intval($_POST['posts_per_page'] ?? 12);

    // Build query arguments
    $args = array(
        'post_type' => 'projects',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $paged,
        'meta_query' => array(),
        'tax_query' => array()
    );

    // Add taxonomy filters if specified
    if (!empty($project_year)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'project-year',
            'field' => 'slug',
            'terms' => $project_year
        );
    }

    if (!empty($industry_category)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'industry-category',
            'field' => 'slug',
            'terms' => $industry_category
        );
    }

    // Set tax_query relation if multiple taxonomies
    if (count($args['tax_query']) > 1) {
        $args['tax_query']['relation'] = 'AND';
    }

    // Execute query
    $query = new WP_Query($args);

    $response = array(
        'success' => true,
        'data' => array(
            'posts' => array(),
            'pagination' => array(
                'current_page' => $paged,
                'total_pages' => $query->max_num_pages,
                'total_posts' => $query->found_posts
            )
        )
    );

    // Generate HTML for each project
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();

            // Get taxonomy terms
            $year_terms = get_the_terms(get_the_ID(), 'project-year');
            $industry_terms = get_the_terms(get_the_ID(), 'industry-category');

            // Build CSS classes for filtering
            $css_classes = array('gb-loop-item', 'general-box-shadow', 'general-border');

            if ($year_terms && !is_wp_error($year_terms)) {
                foreach ($year_terms as $term) {
                    $css_classes[] = 'project-year-' . $term->slug;
                }
            }

            if ($industry_terms && !is_wp_error($industry_terms)) {
                foreach ($industry_terms as $term) {
                    $css_classes[] = 'industry-' . $term->slug;
                }
            }

            // Get ACF fields
            $end_user = get_field('end-user');
            $location = get_field('location');
            $scope = get_field('scope');
            $technologies = get_field('technologies');

            // Generate project HTML (matching the existing structure)
            $project_html = generate_project_card_html(
                get_the_ID(),
                get_the_title(),
                $end_user,
                $location,
                $year_terms,
                $scope,
                $css_classes
            );

            $response['data']['posts'][] = $project_html;
        }
    }

    wp_reset_postdata();

    wp_send_json($response);
}

function generate_project_card_html($post_id, $title, $end_user, $location, $year_terms, $scope, $css_classes) {
    $year_display = '';
    if ($year_terms && !is_wp_error($year_terms)) {
        $year_display = $year_terms[0]->name;
    }

    $scope_list = '';
    if ($scope && is_array($scope)) {
        $scope_list = '<ul>';
        foreach ($scope as $scope_item) {
            $scope_list .= '<li>' . esc_html($scope_item) . '</li>';
        }
        $scope_list .= '</ul>';
    }

    $class_string = implode(' ', $css_classes);

    ob_start();
    ?>
    <div class="<?php echo esc_attr($class_string); ?>">
        <div class="project-card">
            <h3 class="project-title"><?php echo esc_html($title); ?></h3>
            <div class="project-status">✓ Completed</div>

            <?php if ($end_user): ?>
                <div class="project-field">
                    <strong>End User:</strong> <?php echo esc_html($end_user); ?>
                </div>
            <?php endif; ?>

            <?php if ($location): ?>
                <div class="project-field">
                    <strong>Location:</strong> <?php echo esc_html($location); ?>
                </div>
            <?php endif; ?>

            <?php if ($year_display): ?>
                <div class="project-field">
                    <strong>Year:</strong> <?php echo esc_html($year_display); ?>
                </div>
            <?php endif; ?>

            <?php if ($scope_list): ?>
                <div class="project-field">
                    <strong>Scope:</strong>
                    <?php echo $scope_list; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

// Shortcode for project filters
function project_filter_shortcode($atts) {
    $atts = shortcode_atts(array(
        'target_query' => '.projects-query', // CSS selector for the Query Loop container
    ), $atts);

    // Get all project years
    $year_terms = get_terms(array(
        'taxonomy' => 'project-year',
        'hide_empty' => true,
        'orderby' => 'name',
        'order' => 'DESC'
    ));

    // Get all industry categories
    $industry_terms = get_terms(array(
        'taxonomy' => 'industry-category',
        'hide_empty' => true,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    ob_start();
    ?>
    <div class="project-filters" data-target="<?php echo esc_attr($atts['target_query']); ?>">
        <div class="filter-controls">
            <span class="filter-label">Filter by:</span>

            <div class="filter-group">
                <select id="project-year-filter" name="project_year" class="filter-select">
                    <option value="">All Years</option>
                    <?php if ($year_terms && !is_wp_error($year_terms)): ?>
                        <?php foreach ($year_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <select id="industry-category-filter" name="industry_category" class="filter-select">
                    <option value="">All Industries</option>
                    <?php if ($industry_terms && !is_wp_error($industry_terms)): ?>
                        <?php foreach ($industry_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <button type="button" id="clear-filters" class="clear-filters-btn">
                    Clear Filters
                </button>
            </div>
        </div>

        <div class="filter-loading" style="display: none;">
            <span>Loading...</span>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('project_filters', 'project_filter_shortcode');

// Helper function to get pagination HTML
function get_project_pagination_html($current_page, $total_pages, $base_url = '') {
    if ($total_pages <= 1) {
        return '';
    }

    ob_start();
    ?>
    <div class="project-pagination">
        <?php if ($current_page > 1): ?>
            <a href="#" class="pagination-link" data-page="<?php echo ($current_page - 1); ?>">
                &laquo; Previous
            </a>
        <?php endif; ?>

        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
            <?php if ($i == $current_page): ?>
                <span class="pagination-current"><?php echo $i; ?></span>
            <?php else: ?>
                <a href="#" class="pagination-link" data-page="<?php echo $i; ?>">
                    <?php echo $i; ?>
                </a>
            <?php endif; ?>
        <?php endfor; ?>

        <?php if ($current_page < $total_pages): ?>
            <a href="#" class="pagination-link" data-page="<?php echo ($current_page + 1); ?>">
                Next &raquo;
            </a>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}

// Add custom body class when filters are active
function add_project_filter_body_class($classes) {
    if (is_page() && has_shortcode(get_post()->post_content, 'project_filters')) {
        $classes[] = 'has-project-filters';
    }
    return $classes;
}
add_filter('body_class', 'add_project_filter_body_class');

// Ensure taxonomies exist and are properly registered
function ensure_project_taxonomies() {
    // This is a helper function to verify taxonomies exist
    // You may need to register these taxonomies if they don't exist

    if (!taxonomy_exists('project-year')) {
        // Register project-year taxonomy if it doesn't exist
        register_taxonomy('project-year', 'projects', array(
            'label' => 'Project Year',
            'public' => true,
            'hierarchical' => false,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'project-year'),
        ));
    }

    if (!taxonomy_exists('industry-category')) {
        // Register industry-category taxonomy if it doesn't exist
        register_taxonomy('industry-category', 'projects', array(
            'label' => 'Industry Category',
            'public' => true,
            'hierarchical' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'industry-category'),
        ));
    }
}
add_action('init', 'ensure_project_taxonomies');

// Debug function to help troubleshoot issues
function debug_project_filter_data() {
    if (defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options')) {
        if (isset($_GET['debug_project_filters'])) {
            $projects = get_posts(array(
                'post_type' => 'projects',
                'numberposts' => 5,
                'post_status' => 'publish'
            ));

            echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
            echo '<h3>Project Filter Debug Info</h3>';
            echo '<p><strong>Total Projects:</strong> ' . wp_count_posts('projects')->publish . '</p>';

            $year_terms = get_terms(array('taxonomy' => 'project-year', 'hide_empty' => false));
            echo '<p><strong>Project Years:</strong> ' . count($year_terms) . '</p>';

            $industry_terms = get_terms(array('taxonomy' => 'industry-category', 'hide_empty' => false));
            echo '<p><strong>Industry Categories:</strong> ' . count($industry_terms) . '</p>';

            if (!empty($projects)) {
                echo '<p><strong>Sample Project Data:</strong></p>';
                foreach ($projects as $project) {
                    echo '<div style="margin-left: 20px; margin-bottom: 10px;">';
                    echo '<strong>' . $project->post_title . '</strong><br>';

                    $years = get_the_terms($project->ID, 'project-year');
                    if ($years && !is_wp_error($years)) {
                        echo 'Year: ' . implode(', ', wp_list_pluck($years, 'name')) . '<br>';
                    }

                    $industries = get_the_terms($project->ID, 'industry-category');
                    if ($industries && !is_wp_error($industries)) {
                        echo 'Industry: ' . implode(', ', wp_list_pluck($industries, 'name')) . '<br>';
                    }

                    echo '</div>';
                }
            }
            echo '</div>';
        }
    }
}
add_action('wp_footer', 'debug_project_filter_data');

<?php
/**
 * GeneratePress child theme functions and definitions.
 *
 * Add your custom PHP in this file.
 * Only edit this file if you have direct access to it on your server (to fix errors if they happen).
 */

// Enqueue scripts and styles for project filtering
function enqueue_project_filter_assets() {
    // Enqueue the JavaScript file
    wp_enqueue_script(
        'project-filter-js',
        get_stylesheet_directory_uri() . '/js/project-filter.js',
        array('jquery'),
        '1.0.1', // Updated version for cache busting
        true
    );

    // Enqueue the CSS file
    wp_enqueue_style(
        'project-filter-css',
        get_stylesheet_directory_uri() . '/css/project-filter.css',
        array(),
        '1.0.1' // Updated version for cache busting
    );

    // Enqueue debug script if WP_DEBUG is enabled
    if (defined('WP_DEBUG') && WP_DEBUG) {
        wp_enqueue_script(
            'project-filter-debug',
            get_stylesheet_directory_uri() . '/debug-filter.js',
            array('jquery', 'project-filter-js'),
            '1.0.0',
            true
        );
    }

    // Localize script for AJAX
    wp_localize_script('project-filter-js', 'project_filter_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('project_filter_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_project_filter_assets');

// Note: Using client-side filtering to preserve Query Loop design
// No AJAX handler needed for filtering

// Note: Project card HTML is preserved from Query Loop block design

// Shortcode for project filters
function project_filter_shortcode($atts) {
    $atts = shortcode_atts(array(
        'target_query' => '.projects-query', // CSS selector for the Query Loop container
    ), $atts);

    // Get all project years (only those with projects)
    $year_terms = get_terms(array(
        'taxonomy' => 'project-year',
        'hide_empty' => true,
        'orderby' => 'name',
        'order' => 'DESC'
    ));

    // Get all industry categories
    $industry_terms = get_terms(array(
        'taxonomy' => 'industry',
        'hide_empty' => true,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    ob_start();
    ?>
    <div class="project-filters" data-target="<?php echo esc_attr($atts['target_query']); ?>">
        <div class="filter-controls">
            <span class="filter-label">Filter by:</span>

            <div class="filter-group">
                <select id="project-year-filter" name="project_year" class="filter-select">
                    <option value="">All Years</option>
                    <?php if ($year_terms && !is_wp_error($year_terms)): ?>
                        <?php foreach ($year_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->name); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <select id="industry-filter" name="industry" class="filter-select">
                    <option value="">All Industries</option>
                    <?php if ($industry_terms && !is_wp_error($industry_terms)): ?>
                        <?php foreach ($industry_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <button type="button" id="clear-filters" class="clear-filters-btn">
                    Clear Filters
                </button>
            </div>
        </div>

        <div class="filter-loading" style="display: none;">
            <span>Loading...</span>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('project_filters', 'project_filter_shortcode');

// Helper function to get pagination HTML
function get_project_pagination_html($current_page, $total_pages, $base_url = '') {
    if ($total_pages <= 1) {
        return '';
    }

    ob_start();
    ?>
    <div class="project-pagination">
        <?php if ($current_page > 1): ?>
            <a href="#" class="pagination-link" data-page="<?php echo ($current_page - 1); ?>">
                &laquo; Previous
            </a>
        <?php endif; ?>

        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
            <?php if ($i == $current_page): ?>
                <span class="pagination-current"><?php echo $i; ?></span>
            <?php else: ?>
                <a href="#" class="pagination-link" data-page="<?php echo $i; ?>">
                    <?php echo $i; ?>
                </a>
            <?php endif; ?>
        <?php endfor; ?>

        <?php if ($current_page < $total_pages): ?>
            <a href="#" class="pagination-link" data-page="<?php echo ($current_page + 1); ?>">
                Next &raquo;
            </a>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}

// Add custom body class when filters are active
function add_project_filter_body_class($classes) {
    if (is_page() && has_shortcode(get_post()->post_content, 'project_filters')) {
        $classes[] = 'has-project-filters';
    }
    return $classes;
}
add_filter('body_class', 'add_project_filter_body_class');

// Ensure taxonomies exist and are properly registered
function ensure_project_taxonomies() {
    // This is a helper function to verify taxonomies exist
    // You may need to register these taxonomies if they don't exist

    if (!taxonomy_exists('project-year')) {
        // Register project-year taxonomy if it doesn't exist
        register_taxonomy('project-year', 'projects', array(
            'label' => 'Project Year',
            'public' => true,
            'hierarchical' => false,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'project-year'),
        ));
    }

    if (!taxonomy_exists('industry')) {
        // Register industry taxonomy if it doesn't exist
        register_taxonomy('industry', 'projects', array(
            'label' => 'Industry',
            'public' => true,
            'hierarchical' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'industry'),
        ));
    }
}
add_action('init', 'ensure_project_taxonomies');

// Debug function to help troubleshoot issues
function debug_project_filter_data() {
    if (defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options')) {
        if (isset($_GET['debug_project_filters'])) {
            $projects = get_posts(array(
                'post_type' => 'projects',
                'numberposts' => 5,
                'post_status' => 'publish'
            ));

            echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
            echo '<h3>Project Filter Debug Info</h3>';
            echo '<p><strong>Total Projects:</strong> ' . wp_count_posts('projects')->publish . '</p>';

            $year_terms = get_terms(array('taxonomy' => 'project-year', 'hide_empty' => false));
            echo '<p><strong>Project Years:</strong> ' . count($year_terms) . '</p>';

            $industry_terms = get_terms(array('taxonomy' => 'industry', 'hide_empty' => false));
            echo '<p><strong>Industries:</strong> ' . count($industry_terms) . '</p>';

            if (!empty($projects)) {
                echo '<p><strong>Sample Project Data:</strong></p>';
                foreach ($projects as $project) {
                    echo '<div style="margin-left: 20px; margin-bottom: 10px;">';
                    echo '<strong>' . $project->post_title . '</strong><br>';

                    $years = get_the_terms($project->ID, 'project-year');
                    if ($years && !is_wp_error($years)) {
                        echo 'Year: ' . implode(', ', wp_list_pluck($years, 'name')) . '<br>';
                    }

                    $industries = get_the_terms($project->ID, 'industry');
                    if ($industries && !is_wp_error($industries)) {
                        echo 'Industry: ' . implode(', ', wp_list_pluck($industries, 'name')) . '<br>';
                    }

                    echo '</div>';
                }
            }
            echo '</div>';
        }
    }
}
add_action('wp_footer', 'debug_project_filter_data');

// Add project data for filtering - using existing WordPress classes
function add_project_filter_data_script() {
    if (is_page() && has_shortcode(get_post()->post_content, 'project_filters')) {
        // Get all projects with their taxonomy data
        $projects = get_posts(array(
            'post_type' => 'projects',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));

        $project_data = array();
        $year_mapping = array();
        $industry_mapping = array();

        foreach ($projects as $project) {
            $year_terms = get_the_terms($project->ID, 'project-year');
            $industry_terms = get_the_terms($project->ID, 'industry');

            // Store mapping for filter options
            if ($year_terms && !is_wp_error($year_terms)) {
                foreach ($year_terms as $term) {
                    $year_mapping[$term->name] = $term->term_id;
                }
            }

            if ($industry_terms && !is_wp_error($industry_terms)) {
                foreach ($industry_terms as $term) {
                    // Clean up HTML entities in term names
                    $clean_name = html_entity_decode($term->name, ENT_QUOTES, 'UTF-8');
                    $industry_mapping[$clean_name] = $term->slug;
                }
            }

            $project_data[] = array(
                'id' => $project->ID,
                'title' => $project->post_title,
                'slug' => $project->post_name
            );
        }
        ?>
        <script type="text/javascript">
        window.projectFilterData = <?php echo json_encode($project_data); ?>;
        window.yearMapping = <?php echo json_encode($year_mapping); ?>;
        window.industryMapping = <?php echo json_encode($industry_mapping); ?>;

        jQuery(document).ready(function($) {
            // No need to add classes - WordPress already adds them!
            // We'll use the existing classes like project-year-25, industry-water-wastewater

            console.log('Project filter data loaded:', window.projectFilterData);
            console.log('Year mapping:', window.yearMapping);
            console.log('Industry mapping:', window.industryMapping);

            // Debug existing classes
            $('.gb-loop-item').each(function(index) {
                const $item = $(this);
                console.log(`Project ${index + 1} classes:`, $item.attr('class'));
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'add_project_filter_data_script');

// Note: Project classes are now added via client-side JavaScript using project data

// AJAX handler for project filtering
function handle_project_filter_ajax() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'project_filter_nonce')) {
        wp_die('Security check failed');
    }

    // Get filter parameters
    $project_year = sanitize_text_field($_POST['project_year'] ?? '');
    $industry = sanitize_text_field($_POST['industry'] ?? '');
    $paged = intval($_POST['paged'] ?? 1);
    $posts_per_page = intval($_POST['posts_per_page'] ?? 12);

    // Debug logging
    error_log('AJAX Filter - Year: ' . $project_year . ', Industry: ' . $industry . ', Page: ' . $paged);

    // Build query arguments
    $args = array(
        'post_type' => 'projects',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $paged,
        'tax_query' => array()
    );

    // Add taxonomy filters if specified
    if (!empty($project_year)) {
        // Convert year name to term ID using mapping
        $year_terms = get_terms(array(
            'taxonomy' => 'project-year',
            'name' => $project_year,
            'hide_empty' => false
        ));

        // Debug logging
        error_log('Filter year: ' . $project_year);
        error_log('Found year terms: ' . print_r($year_terms, true));

        if (!empty($year_terms) && !is_wp_error($year_terms)) {
            $args['tax_query'][] = array(
                'taxonomy' => 'project-year',
                'field' => 'term_id',
                'terms' => $year_terms[0]->term_id
            );
            error_log('Added year filter for term ID: ' . $year_terms[0]->term_id);
        } else {
            error_log('No year terms found for: ' . $project_year);
        }
    }

    if (!empty($industry)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'industry',
            'field' => 'slug',
            'terms' => $industry
        );
        error_log('Added industry filter for: ' . $industry);
    }

    // Set tax_query relation to AND if multiple filters
    if (count($args['tax_query']) > 1) {
        $args['tax_query']['relation'] = 'AND';
    }

    // Debug the final query
    error_log('Final query args: ' . print_r($args, true));

    // Execute query
    $query = new WP_Query($args);

    // Prepare response
    $response = array(
        'success' => true,
        'data' => array(
            'posts' => array(),
            'pagination' => array(
                'current_page' => $paged,
                'total_pages' => $query->max_num_pages,
                'total_posts' => $query->found_posts
            )
        )
    );

    error_log('Query found posts: ' . $query->found_posts);

    // Generate HTML using the exact same structure as Query Loop
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $post_id = get_the_ID();

            // Get taxonomy terms for CSS classes
            $year_terms = get_the_terms($post_id, 'project-year');
            $industry_terms = get_the_terms($post_id, 'industry');

            // Build CSS classes to match WordPress structure exactly
            $css_classes = array('gb-loop-item', 'general-box-shadow', 'general-border', 'gb-loop-item-4e17580f');
            $css_classes[] = 'post-' . $post_id;
            $css_classes[] = 'projects';
            $css_classes[] = 'type-projects';
            $css_classes[] = 'status-publish';
            $css_classes[] = 'hentry';

            if ($year_terms && !is_wp_error($year_terms)) {
                foreach ($year_terms as $term) {
                    $css_classes[] = 'project-year-' . $term->term_id;
                }
            }

            if ($industry_terms && !is_wp_error($industry_terms)) {
                foreach ($industry_terms as $term) {
                    $css_classes[] = 'industry-' . $term->slug;
                }
            }

            // Set up global post data for template functions
            global $post;
            $post = get_post($post_id);
            setup_postdata($post);

            // Use template part approach - try to render using the same method as Query Loop
            ob_start();

            // This will render the project using the same template as your Query Loop
            // We'll use the same structure but let WordPress handle the rendering
            ?>
            <div class="<?php echo esc_attr(implode(' ', $css_classes)); ?>">
                <?php
                // Try to use the same template part that Query Loop uses
                // If you have a custom template part, use it here
                if (locate_template('template-parts/project-card.php')) {
                    get_template_part('template-parts/project-card');
                } else {
                    // Fallback: render using the same structure as your original Query Loop
                    // This should match exactly what your Query Loop block generates
                    ?>
                    <div class="gb-container gb-container-4e17580f">
                        <div class="gb-inside-container">
                            <?php
                            // Get ACF fields
                            $end_user = get_field('end-user');
                            $location = get_field('location');
                            $scope = get_field('scope');
                            $year_display = '';
                            if ($year_terms && !is_wp_error($year_terms)) {
                                $year_display = $year_terms[0]->name;
                            }
                            ?>

                            <!-- Project Title and Status -->
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1.5rem;">
                                <h3 style="margin: 0; font-size: 1.875rem; font-weight: 600; line-height: 1.2; color: #1a202c;">
                                    <?php the_title(); ?>
                                </h3>
                                <div style="display: flex; align-items: center; color: #38a169; font-size: 1rem; margin-left: 1rem;">
                                    <span style="margin-right: 0.5rem;">✓</span>
                                    <span>Completed</span>
                                </div>
                            </div>

                            <!-- Project Details -->
                            <?php if ($end_user): ?>
                                <div style="margin-bottom: 1rem; font-size: 1.125rem; color: #4a5568;">
                                    <span style="color: #2d3748; font-weight: 500;">End User :</span>
                                    <span style="margin-left: 0.5rem;"><?php echo esc_html($end_user); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if ($location): ?>
                                <div style="margin-bottom: 1rem; font-size: 1.125rem; color: #4a5568;">
                                    <span style="color: #2d3748; font-weight: 500;">Location :</span>
                                    <span style="margin-left: 0.5rem;"><?php echo esc_html($location); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if ($year_display): ?>
                                <div style="margin-bottom: 1rem; font-size: 1.125rem; color: #4a5568;">
                                    <span style="color: #2d3748; font-weight: 500;">Year :</span>
                                    <span style="margin-left: 0.5rem;"><?php echo esc_html($year_display); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if ($scope && is_array($scope) && !empty($scope)): ?>
                                <div style="margin-bottom: 1rem;">
                                    <div style="color: #2d3748; font-weight: 500; margin-bottom: 0.75rem; font-size: 1.125rem;">Scope:</div>
                                    <ul style="margin: 0; padding-left: 1.5rem; list-style-type: disc;">
                                        <?php foreach ($scope as $scope_item): ?>
                                            <li style="margin-bottom: 0.5rem; color: #2d3748; font-size: 1rem;"><?php echo esc_html($scope_item); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>
            <?php

            $project_html = ob_get_clean();
            wp_reset_postdata();

            $response['data']['posts'][] = $project_html;
        }
    }

    wp_reset_postdata();

    // Return JSON response
    wp_send_json($response);
}

// Register AJAX handlers
add_action('wp_ajax_filter_projects', 'handle_project_filter_ajax');
add_action('wp_ajax_nopriv_filter_projects', 'handle_project_filter_ajax');

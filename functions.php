<?php
/**
 * GeneratePress child theme functions and definitions.
 *
 * Add your custom PHP in this file.
 * Only edit this file if you have direct access to it on your server (to fix errors if they happen).
 */

// Enqueue scripts and styles for project filtering
function enqueue_project_filter_assets() {
    // Enqueue the JavaScript file
    wp_enqueue_script(
        'project-filter-js',
        get_stylesheet_directory_uri() . '/js/project-filter.js',
        array('jquery'),
        '1.0.1', // Updated version for cache busting
        true
    );

    // Enqueue the CSS file
    wp_enqueue_style(
        'project-filter-css',
        get_stylesheet_directory_uri() . '/css/project-filter.css',
        array(),
        '1.0.1' // Updated version for cache busting
    );

    // Enqueue debug script if WP_DEBUG is enabled
    if (defined('WP_DEBUG') && WP_DEBUG) {
        wp_enqueue_script(
            'project-filter-debug',
            get_stylesheet_directory_uri() . '/debug-filter.js',
            array('jquery', 'project-filter-js'),
            '1.0.0',
            true
        );
    }

    // Localize script for AJAX
    wp_localize_script('project-filter-js', 'project_filter_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('project_filter_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_project_filter_assets');

// Note: Using client-side filtering to preserve Query Loop design
// No AJAX handler needed for filtering

// Note: Project card HTML is preserved from Query Loop block design

// Shortcode for project filters
function project_filter_shortcode($atts) {
    $atts = shortcode_atts(array(
        'target_query' => '.projects-query', // CSS selector for the Query Loop container
    ), $atts);

    // Get all project years (only those with projects)
    $year_terms = get_terms(array(
        'taxonomy' => 'project-year',
        'hide_empty' => true,
        'orderby' => 'name',
        'order' => 'DESC'
    ));

    // Get all industry categories
    $industry_terms = get_terms(array(
        'taxonomy' => 'industry',
        'hide_empty' => true,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    ob_start();
    ?>
    <div class="project-filters" data-target="<?php echo esc_attr($atts['target_query']); ?>">
        <div class="filter-controls">
            <span class="filter-label">Filter by:</span>

            <div class="filter-group">
                <select id="project-year-filter" name="project_year" class="filter-select">
                    <option value="">All Years</option>
                    <?php if ($year_terms && !is_wp_error($year_terms)): ?>
                        <?php foreach ($year_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->name); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <select id="industry-filter" name="industry" class="filter-select">
                    <option value="">All Industries</option>
                    <?php if ($industry_terms && !is_wp_error($industry_terms)): ?>
                        <?php foreach ($industry_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <div class="filter-group">
                <button type="button" id="clear-filters" class="clear-filters-btn">
                    Clear Filters
                </button>
            </div>
        </div>

        <div class="filter-loading" style="display: none;">
            <span>Loading...</span>
        </div>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('project_filters', 'project_filter_shortcode');

// Helper function to get pagination HTML
function get_project_pagination_html($current_page, $total_pages, $base_url = '') {
    if ($total_pages <= 1) {
        return '';
    }

    ob_start();
    ?>
    <div class="project-pagination">
        <?php if ($current_page > 1): ?>
            <a href="#" class="pagination-link" data-page="<?php echo ($current_page - 1); ?>">
                &laquo; Previous
            </a>
        <?php endif; ?>

        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
            <?php if ($i == $current_page): ?>
                <span class="pagination-current"><?php echo $i; ?></span>
            <?php else: ?>
                <a href="#" class="pagination-link" data-page="<?php echo $i; ?>">
                    <?php echo $i; ?>
                </a>
            <?php endif; ?>
        <?php endfor; ?>

        <?php if ($current_page < $total_pages): ?>
            <a href="#" class="pagination-link" data-page="<?php echo ($current_page + 1); ?>">
                Next &raquo;
            </a>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}

// Add custom body class when filters are active
function add_project_filter_body_class($classes) {
    if (is_page() && has_shortcode(get_post()->post_content, 'project_filters')) {
        $classes[] = 'has-project-filters';
    }
    return $classes;
}
add_filter('body_class', 'add_project_filter_body_class');

// Ensure taxonomies exist and are properly registered
function ensure_project_taxonomies() {
    // This is a helper function to verify taxonomies exist
    // You may need to register these taxonomies if they don't exist

    if (!taxonomy_exists('project-year')) {
        // Register project-year taxonomy if it doesn't exist
        register_taxonomy('project-year', 'projects', array(
            'label' => 'Project Year',
            'public' => true,
            'hierarchical' => false,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'project-year'),
        ));
    }

    if (!taxonomy_exists('industry')) {
        // Register industry taxonomy if it doesn't exist
        register_taxonomy('industry', 'projects', array(
            'label' => 'Industry',
            'public' => true,
            'hierarchical' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'industry'),
        ));
    }
}
add_action('init', 'ensure_project_taxonomies');

// Debug function to help troubleshoot issues
function debug_project_filter_data() {
    if (defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options')) {
        if (isset($_GET['debug_project_filters'])) {
            $projects = get_posts(array(
                'post_type' => 'projects',
                'numberposts' => 5,
                'post_status' => 'publish'
            ));

            echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
            echo '<h3>Project Filter Debug Info</h3>';
            echo '<p><strong>Total Projects:</strong> ' . wp_count_posts('projects')->publish . '</p>';

            $year_terms = get_terms(array('taxonomy' => 'project-year', 'hide_empty' => false));
            echo '<p><strong>Project Years:</strong> ' . count($year_terms) . '</p>';

            $industry_terms = get_terms(array('taxonomy' => 'industry', 'hide_empty' => false));
            echo '<p><strong>Industries:</strong> ' . count($industry_terms) . '</p>';

            if (!empty($projects)) {
                echo '<p><strong>Sample Project Data:</strong></p>';
                foreach ($projects as $project) {
                    echo '<div style="margin-left: 20px; margin-bottom: 10px;">';
                    echo '<strong>' . $project->post_title . '</strong><br>';

                    $years = get_the_terms($project->ID, 'project-year');
                    if ($years && !is_wp_error($years)) {
                        echo 'Year: ' . implode(', ', wp_list_pluck($years, 'name')) . '<br>';
                    }

                    $industries = get_the_terms($project->ID, 'industry');
                    if ($industries && !is_wp_error($industries)) {
                        echo 'Industry: ' . implode(', ', wp_list_pluck($industries, 'name')) . '<br>';
                    }

                    echo '</div>';
                }
            }
            echo '</div>';
        }
    }
}
add_action('wp_footer', 'debug_project_filter_data');

// Add project data for filtering - using existing WordPress classes
function add_project_filter_data_script() {
    if (is_page() && has_shortcode(get_post()->post_content, 'project_filters')) {
        // Get all projects with their taxonomy data
        $projects = get_posts(array(
            'post_type' => 'projects',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));

        $project_data = array();
        $year_mapping = array();
        $industry_mapping = array();

        foreach ($projects as $project) {
            $year_terms = get_the_terms($project->ID, 'project-year');
            $industry_terms = get_the_terms($project->ID, 'industry');

            // Store mapping for filter options
            if ($year_terms && !is_wp_error($year_terms)) {
                foreach ($year_terms as $term) {
                    $year_mapping[$term->name] = $term->term_id;
                }
            }

            if ($industry_terms && !is_wp_error($industry_terms)) {
                foreach ($industry_terms as $term) {
                    // Clean up HTML entities in term names
                    $clean_name = html_entity_decode($term->name, ENT_QUOTES, 'UTF-8');
                    $industry_mapping[$clean_name] = $term->slug;
                }
            }

            $project_data[] = array(
                'id' => $project->ID,
                'title' => $project->post_title,
                'slug' => $project->post_name
            );
        }
        ?>
        <script type="text/javascript">
        window.projectFilterData = <?php echo json_encode($project_data); ?>;
        window.yearMapping = <?php echo json_encode($year_mapping); ?>;
        window.industryMapping = <?php echo json_encode($industry_mapping); ?>;

        jQuery(document).ready(function($) {
            // No need to add classes - WordPress already adds them!
            // We'll use the existing classes like project-year-25, industry-water-wastewater

            console.log('Project filter data loaded:', window.projectFilterData);
            console.log('Year mapping:', window.yearMapping);
            console.log('Industry mapping:', window.industryMapping);

            // Debug existing classes
            $('.gb-loop-item').each(function(index) {
                const $item = $(this);
                console.log(`Project ${index + 1} classes:`, $item.attr('class'));
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'add_project_filter_data_script');

// Note: Project classes are now added via client-side JavaScript using project data

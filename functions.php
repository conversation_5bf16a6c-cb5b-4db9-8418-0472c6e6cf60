<?php
/**
 * GeneratePress child theme functions and definitions.
 *
 * Add your custom PHP in this file.
 * Only edit this file if you have direct access to it on your server (to fix errors if they happen).
 */

/**
 * ========================================
 * NATIVE GENERATEBLOCKS QUERY LOOP FILTER
 * ========================================
 * Much simpler solution using official GenerateBlocks hook
 * Based on GenerateBlocks support documentation
 *
 * BACKUP: Previous complex solution backed up to /tmp/generatepress-child-backup-20250623-174144.zip
 */

// Filter GenerateBlocks Query Loop using native hook
add_filter( 'generateblocks_query_loop_args', 'native_project_filter', 10, 2 );

function native_project_filter( $query_args, $attributes ) {
    // Target specific Query Loop by CSS class
    $target_class = 'projects-query-loop'; // Add this class to your Query Loop Grid

    // Only apply to our specific Query Loop
    if ( empty( $attributes['className'] ) ||
         strpos( $attributes['className'], $target_class ) === false ) {
        return $query_args;
    }

    // Get filter parameters from URL
    $year_filter = $_GET['project_year'] ?? '';
    $industry_filter = $_GET['industry'] ?? '';

    // Build tax_query array
    $tax_query = array();

    if ( !empty( $year_filter ) ) {
        $tax_query[] = array(
            'taxonomy' => 'project-year',
            'field'    => 'slug',
            'terms'    => sanitize_text_field( $year_filter )
        );
    }

    if ( !empty( $industry_filter ) ) {
        $tax_query[] = array(
            'taxonomy' => 'industry',
            'field'    => 'slug',
            'terms'    => sanitize_text_field( $industry_filter )
        );
    }

    // Apply filters if any exist
    if ( !empty( $tax_query ) ) {
        if ( count( $tax_query ) > 1 ) {
            $tax_query['relation'] = 'AND';
        }

        $query_args['tax_query'] = $tax_query;

        // Add sorting by year (newest first) - using post date as proxy
        $query_args['orderby'] = 'date';
        $query_args['order'] = 'DESC';
    }

    return $query_args;
}

// Simple form for filtering (no AJAX needed)
function render_simple_project_filter() {
    $current_year = $_GET['project_year'] ?? '';
    $current_industry = $_GET['industry'] ?? '';

    ?>
    <form method="GET" class="simple-project-filter" style="margin-bottom: 2rem;">
        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
            <label style="font-weight: bold;">Filter by:</label>

            <select name="project_year" style="padding: 0.5rem;">
                <option value="">All Years</option>
                <?php
                $years = get_terms(array(
                    'taxonomy' => 'project-year',
                    'hide_empty' => true,
                    'orderby' => 'name',
                    'order' => 'DESC'
                ));
                foreach ($years as $year) {
                    $selected = ($current_year === $year->slug) ? 'selected' : '';
                    echo "<option value='{$year->slug}' {$selected}>{$year->name}</option>";
                }
                ?>
            </select>

            <select name="industry" style="padding: 0.5rem;">
                <option value="">All Industries</option>
                <?php
                $industries = get_terms(array(
                    'taxonomy' => 'industry',
                    'hide_empty' => true
                ));
                foreach ($industries as $industry) {
                    $selected = ($current_industry === $industry->slug) ? 'selected' : '';
                    echo "<option value='{$industry->slug}' {$selected}>{$industry->name}</option>";
                }
                ?>
            </select>

            <button type="submit" style="padding: 0.5rem 1rem; background: #0073aa; color: white; border: none; cursor: pointer;">Filter</button>
            <a href="<?php echo remove_query_arg(array('project_year', 'industry')); ?>" style="padding: 0.5rem 1rem; background: #666; color: white; text-decoration: none;">Clear</a>
        </div>

        <?php
        // Show filter results count
        if (!empty($current_year) || !empty($current_industry)) {
            global $wp_query;
            $total_posts = $wp_query->found_posts ?? 0;
            echo "<p style='margin-top: 1rem; font-weight: bold;'>Showing {$total_posts} projects</p>";
        }
        ?>
    </form>
    <?php
}

// Shortcode to display filter form
add_shortcode('project_filter', 'render_simple_project_filter');

// Ensure taxonomies exist and are properly registered
function ensure_project_taxonomies() {
    if (!taxonomy_exists('project-year')) {
        register_taxonomy('project-year', 'projects', array(
            'label' => 'Project Year',
            'public' => true,
            'hierarchical' => false,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'project-year'),
        ));
    }

    if (!taxonomy_exists('industry')) {
        register_taxonomy('industry', 'projects', array(
            'label' => 'Industry',
            'public' => true,
            'hierarchical' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'industry'),
        ));
    }
}
add_action('init', 'ensure_project_taxonomies');

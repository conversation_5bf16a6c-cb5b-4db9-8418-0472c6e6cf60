# Project Filters Implementation

This implementation provides AJAX-based filtering functionality for WordPress "Projects" pages using GenerateBlocks 2.0 Query Loop blocks.

## Features

- **AJAX Filtering**: Filter projects by year and industry category without page reloads
- **Combined Filters**: Use multiple filters simultaneously (year AND industry)
- **Pagination**: Maintains pagination functionality with 12 projects per page
- **Responsive Design**: Mobile-friendly filter controls
- **Reset Functionality**: Clear all filters with one click
- **Loading States**: Visual feedback during filtering operations
- **Error Handling**: Graceful error handling and user feedback

## Installation

1. **Files Added**:
   - `functions.php` - Updated with AJAX handlers and shortcode
   - `js/project-filter.js` - JavaScript for AJAX functionality
   - `css/project-filter.css` - Styling for filter controls

2. **Dependencies**:
   - jQuery (included with WordPress)
   - GenerateBlocks Query Loop block
   - Custom post type "projects"
   - Taxonomies: "project-year" and "industry-category"
   - ACF fields: end-user, location, scope, technologies

## Usage

### 1. Add Filter Controls

Use the shortcode above your Query Loop block:

```
[project_filters target_query=".projects-query"]
```

**Parameters**:
- `target_query` (optional): CSS selector for the Query Loop container. Default: `.projects-query`

### 2. Query Loop Setup

Ensure your GenerateBlocks Query Loop block:
- Displays custom post type "projects"
- Shows 12 posts per page
- Has a CSS class that matches the `target_query` parameter

### 3. Required HTML Structure

The filter expects this structure in your Query Loop:
```html
<div class="projects-query">
    <div class="gb-looper-[id]">
        <!-- Project cards will be inserted here -->
    </div>
</div>
```

## Customization

### Filter Controls Styling

Modify `css/project-filter.css` to match your theme:

```css
.project-filters {
    /* Customize filter container */
}

.filter-select {
    /* Customize dropdown styling */
}

.clear-filters-btn {
    /* Customize reset button */
}
```

### Project Card Template

The project card HTML is generated in the `generate_project_card_html()` function in `functions.php`. Modify this function to match your desired card layout.

### Additional Filters

To add more filter options, modify:

1. **PHP**: Add new filter controls in `project_filter_shortcode()`
2. **JavaScript**: Update `getFilterData()` and AJAX data
3. **AJAX Handler**: Add new parameters to `handle_project_filter_ajax()`

## Troubleshooting

### Debug Mode

Add `?debug_project_filters=1` to your URL to see debug information (requires admin privileges and WP_DEBUG enabled).

### Common Issues

1. **Filters not appearing**: Ensure taxonomies exist and have terms
2. **AJAX not working**: Check browser console for JavaScript errors
3. **Styling issues**: Verify CSS file is loading correctly
4. **No results**: Check that projects have the required taxonomy terms

### Required Taxonomies

The implementation expects these taxonomies to exist:
- `project-year` (non-hierarchical)
- `industry-category` (hierarchical)

If they don't exist, the code will attempt to register them automatically.

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Internet Explorer 11+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Notes

- AJAX requests are cached by WordPress
- Pagination reduces server load
- Loading states improve perceived performance
- Minimal DOM manipulation for smooth updates

## Security

- AJAX requests use WordPress nonces for security
- All input is sanitized before database queries
- Follows WordPress coding standards

## Future Enhancements

Potential improvements:
- URL parameter support for bookmarkable filters
- Advanced filters (location, scope, technologies)
- Filter result counts
- Infinite scroll option
- Search functionality

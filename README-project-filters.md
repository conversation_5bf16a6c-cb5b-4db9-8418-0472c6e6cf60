# Project Filters Implementation

This implementation provides client-side filtering functionality for WordPress "Projects" pages using GenerateBlocks 2.0 Query Loop blocks while preserving the original design.

## Features

- **Client-Side Filtering**: Filter projects by year and industry without page reloads
- **Combined Filters**: Use multiple filters simultaneously (year AND industry)
- **Design Preservation**: Maintains original Query Loop block design and styling
- **Responsive Design**: Mobile-friendly filter controls
- **Reset Functionality**: Clear all filters with one click
- **Loading States**: Visual feedback during filtering operations
- **Error Handling**: Graceful error handling and user feedback

## Installation

1. **Files Added**:
   - `functions.php` - Updated with AJAX handlers and shortcode
   - `js/project-filter.js` - JavaScript for AJAX functionality
   - `css/project-filter.css` - Styling for filter controls

2. **Dependencies**:
   - jQuery (included with WordPress)
   - GenerateBlocks Query Loop block
   - Custom post type "projects"
   - Taxonomies: "project-year" and "industry"
   - ACF fields: end-user, location, scope, technologies

## Usage

### 1. Add Filter Controls

Use the shortcode above your Query Loop block:

```
[project_filters target_query=".projects-query"]
```

**Parameters**:
- `target_query` (optional): CSS selector for the Query Loop container. Default: `.projects-query`

### 2. Query Loop Setup

Ensure your GenerateBlocks Query Loop block:
- Displays custom post type "projects"
- Shows 12 posts per page
- Has a CSS class that matches the `target_query` parameter

### 3. Required HTML Structure

The filter expects this structure in your Query Loop:
```html
<div class="projects-query">
    <div class="gb-looper-[id]">
        <!-- Project cards will be inserted here -->
    </div>
</div>
```

## Customization

### Filter Controls Styling

Modify `css/project-filter.css` to match your theme:

```css
.project-filters {
    /* Customize filter container */
}

.filter-select {
    /* Customize dropdown styling */
}

.clear-filters-btn {
    /* Customize reset button */
}
```

### Project Card Template

The implementation preserves your original Query Loop block design. No modifications to the card template are needed.

### Additional Filters

To add more filter options, modify:

1. **PHP**: Add new filter controls in `project_filter_shortcode()`
2. **JavaScript**: Update `getFilterData()` and filtering logic

## Troubleshooting

### Debug Mode

Add `?debug_project_filters=1` to your URL to see debug information (requires admin privileges and WP_DEBUG enabled).

### Common Issues

1. **Filters not appearing**: Ensure taxonomies exist and have terms
2. **AJAX not working**: Check browser console for JavaScript errors
3. **Styling issues**: Verify CSS file is loading correctly
4. **No results**: Check that projects have the required taxonomy terms

### Required Taxonomies

The implementation expects these taxonomies to exist:
- `project-year` (non-hierarchical)
- `industry` (hierarchical)

If they don't exist, the code will attempt to register them automatically.

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Internet Explorer 11+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Notes

- Client-side filtering provides instant results
- No server requests during filtering
- Loading states improve perceived performance
- Minimal DOM manipulation for smooth updates
- Preserves original Query Loop performance

## Security

- Client-side filtering eliminates security concerns
- No user input sent to server during filtering
- Follows WordPress coding standards

## Future Enhancements

Potential improvements:
- URL parameter support for bookmarkable filters
- Advanced filters (location, scope, technologies)
- Filter result counts
- Infinite scroll option
- Search functionality

## 🔗 Integration Notes

### With GenerateBlocks Query Loop
The implementation preserves the original Query Loop block design completely. No HTML is generated or replaced.

### With ACF Fields
All ACF fields (end-user, location, scope, technologies) are supported in the original Query Loop design.

### With WordPress Caching
The implementation is compatible with WordPress caching plugins. Client-side filtering works regardless of caching.

## 📞 Support
If you encounter issues:
1. Check the README-project-filters.md for detailed documentation
2. Use the debug mode: `?debug_project_filters=1`
3. Review browser console for JavaScript errors
4. Verify all prerequisites are met

jQuery(document).ready(function($) {
    'use strict';
    
    // Configuration
    const config = {
        filterContainer: '.project-filters',
        queryContainer: '.projects-query',
        loaderClass: '.filter-loading',
        postsPerPage: 12,
        currentPage: 1,
        isLoading: false
    };
    
    // Initialize filters
    function initProjectFilters() {
        const $filterContainer = $(config.filterContainer);
        
        if ($filterContainer.length === 0) {
            return;
        }
        
        // Get target query container from data attribute
        const targetQuery = $filterContainer.data('target');
        if (targetQuery) {
            config.queryContainer = targetQuery;
        }
        
        // Bind filter events
        bindFilterEvents();
        
        // Initialize pagination if exists
        bindPaginationEvents();
    }
    
    // Bind filter control events
    function bindFilterEvents() {
        const $filterContainer = $(config.filterContainer);
        
        // Filter select changes
        $filterContainer.on('change', '.filter-select', function() {
            config.currentPage = 1; // Reset to first page
            performFilter();
        });
        
        // Clear filters button
        $filterContainer.on('click', '#clear-filters', function(e) {
            e.preventDefault();
            clearAllFilters();
        });
    }
    
    // Bind pagination events
    function bindPaginationEvents() {
        $(document).on('click', '.pagination-link', function(e) {
            e.preventDefault();
            
            if (config.isLoading) {
                return;
            }
            
            const page = parseInt($(this).data('page'));
            if (page && page > 0) {
                config.currentPage = page;
                performFilter();
            }
        });
    }
    
    // Perform client-side filtering to preserve Query Loop design
    function performFilter() {
        if (config.isLoading) {
            return;
        }

        config.isLoading = true;
        showLoader();

        // Get current filter values
        const filterData = getFilterData();

        // Perform client-side filtering
        setTimeout(function() {
            try {
                updateQueryResults(filterData);
                // Scroll to results
                scrollToResults();
            } catch (error) {
                console.error('Filter error:', error);
                showError('An error occurred while filtering. Please try again.');
            } finally {
                config.isLoading = false;
                hideLoader();
            }
        }, 300); // Small delay to show loading state
    }
    
    // Get current filter values
    function getFilterData() {
        return {
            projectYear: $('#project-year-filter').val() || '',
            industry: $('#industry-filter').val() || ''
        };
    }
    
    // Update query results by showing/hiding existing elements based on CSS classes
    function updateQueryResults(filterData) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();

        if ($looper.length === 0) {
            console.error('Query loop container not found');
            return;
        }

        // Get all existing project items
        const $allItems = $looper.find('.gb-loop-item');

        // Remove any existing no-results message
        $looper.find('.no-results').remove();

        let visibleCount = 0;

        // Filter items based on CSS classes
        $allItems.each(function() {
            const $item = $(this);
            let shouldShow = true;

            // Check year filter
            if (filterData.projectYear) {
                const yearClass = 'project-year-' + filterData.projectYear;
                if (!$item.hasClass(yearClass)) {
                    shouldShow = false;
                }
            }

            // Check industry filter
            if (filterData.industry) {
                const industryClass = 'industry-' + filterData.industry;
                if (!$item.hasClass(industryClass)) {
                    shouldShow = false;
                }
            }

            if (shouldShow) {
                $item.show();
                visibleCount++;
            } else {
                $item.hide();
            }
        });

        // Show no results message if no items are visible
        if (visibleCount === 0) {
            $looper.append('<div class="no-results"><p>No projects found matching your criteria.</p></div>');
        }

        // Trigger custom event for other scripts
        $(document).trigger('projectsFiltered', [filterData, visibleCount]);
    }
    
    // Note: Pagination is handled by the original Query Loop block
    // Client-side filtering preserves the original pagination structure
    
    // Clear all filters
    function clearAllFilters() {
        $('#project-year-filter').val('');
        $('#industry-filter').val('');
        config.currentPage = 1;
        performFilter();
    }
    
    // Show loading indicator
    function showLoader() {
        $(config.loaderClass).show();
        $(config.queryContainer).addClass('filtering-in-progress');
    }
    
    // Hide loading indicator
    function hideLoader() {
        $(config.loaderClass).hide();
        $(config.queryContainer).removeClass('filtering-in-progress');
    }
    
    // Show error message
    function showError(message) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
        
        if ($looper.length > 0) {
            $looper.html(`<div class="filter-error"><p>${message}</p></div>`);
        }
    }
    
    // Scroll to results
    function scrollToResults() {
        const $queryContainer = $(config.queryContainer);
        if ($queryContainer.length > 0) {
            $('html, body').animate({
                scrollTop: $queryContainer.offset().top - 100
            }, 500);
        }
    }
    
    // Initialize when DOM is ready
    initProjectFilters();
    
    // Re-initialize on dynamic content changes (if needed)
    $(document).on('DOMContentLoaded', initProjectFilters);
});
jQuery(document).ready(function($) {
    'use strict';
    
    // Configuration
    const config = {
        filterContainer: '.project-filters',
        queryContainer: '.projects-query',
        loaderClass: '.filter-loading',
        postsPerPage: 12,
        currentPage: 1,
        isLoading: false
    };
    
    // Initialize filters
    function initProjectFilters() {
        const $filterContainer = $(config.filterContainer);
        
        if ($filterContainer.length === 0) {
            return;
        }
        
        // Get target query container from data attribute
        const targetQuery = $filterContainer.data('target');
        if (targetQuery) {
            config.queryContainer = targetQuery;
        }
        
        // Bind filter events
        bindFilterEvents();
        
        // Initialize pagination if exists
        bindPaginationEvents();
    }
    
    // Bind filter control events
    function bindFilterEvents() {
        const $filterContainer = $(config.filterContainer);
        
        // Filter select changes
        $filterContainer.on('change', '.filter-select', function() {
            config.currentPage = 1; // Reset to first page
            performFilter();
        });
        
        // Clear filters button
        $filterContainer.on('click', '#clear-filters', function(e) {
            e.preventDefault();
            clearAllFilters();
        });
    }
    
    // Bind pagination events
    function bindPaginationEvents() {
        $(document).on('click', '.pagination-link', function(e) {
            e.preventDefault();
            
            if (config.isLoading) {
                return;
            }
            
            const page = parseInt($(this).data('page'));
            if (page && page > 0) {
                config.currentPage = page;
                performFilter();
            }
        });
    }
    
    // Perform AJAX filter request
    function performFilter() {
        if (config.isLoading) {
            return;
        }
        
        config.isLoading = true;
        showLoader();
        
        // Get current filter values
        const filterData = getFilterData();
        
        // AJAX request
        $.ajax({
            url: project_filter_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'filter_projects',
                nonce: project_filter_ajax.nonce,
                project_year: filterData.projectYear,
                industry_category: filterData.industryCategory,
                paged: config.currentPage,
                posts_per_page: config.postsPerPage
            },
            success: function(response) {
                if (response.success) {
                    updateQueryResults(response.data);
                    updatePagination(response.data.pagination);
                    
                    // Scroll to results
                    scrollToResults();
                } else {
                    console.error('Filter request failed:', response);
                    showError('Failed to load filtered results. Please try again.');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                showError('An error occurred while filtering. Please try again.');
            },
            complete: function() {
                config.isLoading = false;
                hideLoader();
            }
        });
    }
    
    // Get current filter values
    function getFilterData() {
        return {
            projectYear: $('#project-year-filter').val() || '',
            industryCategory: $('#industry-category-filter').val() || ''
        };
    }
    
    // Update query results in the DOM
    function updateQueryResults(data) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
        
        if ($looper.length === 0) {
            console.error('Query loop container not found');
            return;
        }
        
        // Clear existing content
        $looper.empty();
        
        // Add new content
        if (data.posts && data.posts.length > 0) {
            data.posts.forEach(function(postHtml) {
                $looper.append(postHtml);
            });
        } else {
            $looper.append('<div class="no-results"><p>No projects found matching your criteria.</p></div>');
        }
        
        // Trigger custom event for other scripts
        $(document).trigger('projectsFiltered', [data]);
    }
    
    // Update pagination
    function updatePagination(paginationData) {
        // Remove existing pagination
        $('.project-pagination').remove();
        
        if (paginationData.total_pages > 1) {
            const paginationHtml = generatePaginationHtml(paginationData);
            $(config.queryContainer).after(paginationHtml);
        }
    }
    
    // Generate pagination HTML
    function generatePaginationHtml(pagination) {
        let html = '<div class="project-pagination">';
        
        // Previous button
        if (pagination.current_page > 1) {
            html += `<a href="#" class="pagination-link" data-page="${pagination.current_page - 1}">&laquo; Previous</a>`;
        }
        
        // Page numbers
        for (let i = 1; i <= pagination.total_pages; i++) {
            if (i === pagination.current_page) {
                html += `<span class="pagination-current">${i}</span>`;
            } else {
                html += `<a href="#" class="pagination-link" data-page="${i}">${i}</a>`;
            }
        }
        
        // Next button
        if (pagination.current_page < pagination.total_pages) {
            html += `<a href="#" class="pagination-link" data-page="${pagination.current_page + 1}">Next &raquo;</a>`;
        }
        
        html += '</div>';
        return html;
    }
    
    // Clear all filters
    function clearAllFilters() {
        $('#project-year-filter').val('');
        $('#industry-category-filter').val('');
        config.currentPage = 1;
        performFilter();
    }
    
    // Show loading indicator
    function showLoader() {
        $(config.loaderClass).show();
        $(config.queryContainer).addClass('filtering-in-progress');
    }
    
    // Hide loading indicator
    function hideLoader() {
        $(config.loaderClass).hide();
        $(config.queryContainer).removeClass('filtering-in-progress');
    }
    
    // Show error message
    function showError(message) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
        
        if ($looper.length > 0) {
            $looper.html(`<div class="filter-error"><p>${message}</p></div>`);
        }
    }
    
    // Scroll to results
    function scrollToResults() {
        const $queryContainer = $(config.queryContainer);
        if ($queryContainer.length > 0) {
            $('html, body').animate({
                scrollTop: $queryContainer.offset().top - 100
            }, 500);
        }
    }
    
    // Initialize when DOM is ready
    initProjectFilters();
    
    // Re-initialize on dynamic content changes (if needed)
    $(document).on('DOMContentLoaded', initProjectFilters);
});
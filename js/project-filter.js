jQuery(document).ready(function($) {
    'use strict';
    
    // Configuration
    const config = {
        filterContainer: '.project-filters',
        queryContainer: '.projects-query',
        loaderClass: '.filter-loading',
        postsPerPage: 12,
        currentPage: 1,
        isLoading: false
    };
    
    // Initialize filters
    function initProjectFilters() {
        const $filterContainer = $(config.filterContainer);

        if ($filterContainer.length === 0) {
            return;
        }

        // Get target query container from data attribute
        const targetQuery = $filterContainer.data('target');
        if (targetQuery) {
            config.queryContainer = targetQuery;
        }

        // Bind filter events
        bindFilterEvents();

        // Initialize pagination if exists
        bindPaginationEvents();

        // Show initial count
        showInitialCount();
    }

    // Show initial project count
    function showInitialCount() {
        // Wait a bit for project data to be loaded
        setTimeout(function() {
            const $queryContainer = $(config.queryContainer);
            const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
            const $allItems = $looper.find('.gb-loop-item');

            if ($allItems.length > 0) {
                updateResultsCount($allItems.length, $allItems.length, {});
            }
        }, 500);
    }
    
    // Bind filter control events
    function bindFilterEvents() {
        const $filterContainer = $(config.filterContainer);
        
        // Filter select changes
        $filterContainer.on('change', '.filter-select', function() {
            config.currentPage = 1; // Reset to first page
            performFilter();
        });
        
        // Clear filters button
        $filterContainer.on('click', '#clear-filters', function(e) {
            e.preventDefault();
            clearAllFilters();
        });
    }
    
    // Bind pagination events
    function bindPaginationEvents() {
        $(document).on('click', '.pagination-link', function(e) {
            e.preventDefault();
            
            if (config.isLoading) {
                return;
            }
            
            const page = parseInt($(this).data('page'));
            if (page && page > 0) {
                config.currentPage = page;
                performFilter();
            }
        });
    }
    
    // Perform client-side filtering to preserve Query Loop design
    function performFilter() {
        if (config.isLoading) {
            return;
        }

        config.isLoading = true;
        showLoader();

        // Get current filter values
        const filterData = getFilterData();

        // Perform client-side filtering
        setTimeout(function() {
            try {
                updateQueryResults(filterData);
                // Scroll to results
                scrollToResults();
            } catch (error) {
                console.error('Filter error:', error);
                showError('An error occurred while filtering. Please try again.');
            } finally {
                config.isLoading = false;
                hideLoader();
            }
        }, 300); // Small delay to show loading state
    }
    
    // Get current filter values
    function getFilterData() {
        return {
            projectYear: $('#project-year-filter').val() || '',
            industry: $('#industry-filter').val() || ''
        };
    }
    
    // Update query results by showing/hiding existing elements based on CSS classes
    function updateQueryResults(filterData) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();

        if ($looper.length === 0) {
            console.error('Query loop container not found');
            return;
        }

        // Get all existing project items
        const $allItems = $looper.find('.gb-loop-item');
        const totalItems = $allItems.length;

        // Remove any existing no-results message and filter count
        $looper.find('.no-results').remove();
        $('.filter-results-count').remove();

        let visibleCount = 0;

        // Filter items based on CSS classes
        $allItems.each(function() {
            const $item = $(this);
            let shouldShow = true;

            // Check year filter
            if (filterData.projectYear) {
                const yearClass = 'project-year-' + filterData.projectYear;
                if (!$item.hasClass(yearClass)) {
                    shouldShow = false;
                }
            }

            // Check industry filter
            if (filterData.industry) {
                const industryClass = 'industry-' + filterData.industry;
                if (!$item.hasClass(industryClass)) {
                    shouldShow = false;
                }
            }

            if (shouldShow) {
                $item.show();
                visibleCount++;
            } else {
                $item.hide();
            }
        });

        // Show results count
        updateResultsCount(visibleCount, totalItems, filterData);

        // Show no results message if no items are visible
        if (visibleCount === 0) {
            $looper.append('<div class="no-results"><p>No projects found matching your criteria.</p></div>');
        }

        // Handle pagination visibility
        updatePaginationVisibility(visibleCount);

        // Trigger custom event for other scripts
        $(document).trigger('projectsFiltered', [filterData, visibleCount]);
    }

    // Update results count display
    function updateResultsCount(visibleCount, totalItems, filterData) {
        const $filterContainer = $(config.filterContainer);

        // Create count message
        let countMessage = '';
        const hasActiveFilters = filterData.projectYear || filterData.industry;

        if (hasActiveFilters) {
            countMessage = `Showing ${visibleCount} of ${totalItems} projects`;
        } else {
            countMessage = `Showing all ${totalItems} projects`;
        }

        // Check if count already exists
        let $existingCount = $('.filter-results-count');

        if ($existingCount.length > 0) {
            // Update existing count with animation
            $existingCount.addClass('updated');
            $existingCount.text(countMessage);

            // Remove updated class after animation
            setTimeout(function() {
                $existingCount.removeClass('updated');
            }, 500);
        } else {
            // Add new count display after filter controls
            const countHtml = `<div class="filter-results-count">${countMessage}</div>`;
            $filterContainer.append(countHtml);
        }
    }

    // Update pagination visibility based on filtered results
    function updatePaginationVisibility(visibleCount) {
        const $pagination = $('.gb-query-loop-pagination-projects, .gb-element-05a39879, [class*="gb-query-loop-pagination"]');

        if ($pagination.length === 0) {
            return;
        }

        // Hide pagination if filtered results are less than posts per page
        // or if there are very few results (6 or less)
        const shouldHidePagination = visibleCount <= config.postsPerPage || visibleCount <= 6;

        if (shouldHidePagination) {
            $pagination.hide().addClass('hidden-by-filter');
        } else {
            $pagination.show().removeClass('hidden-by-filter');
        }
    }
    
    // Note: Pagination is handled by the original Query Loop block
    // Client-side filtering preserves the original pagination structure
    
    // Clear all filters
    function clearAllFilters() {
        $('#project-year-filter').val('');
        $('#industry-filter').val('');
        config.currentPage = 1;

        // Show all items and reset pagination
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
        const $allItems = $looper.find('.gb-loop-item');

        $allItems.show();
        $('.filter-results-count').remove();
        $('.no-results').remove();

        // Show pagination again
        $('.gb-query-loop-pagination-projects, .gb-element-05a39879, [class*="gb-query-loop-pagination"]')
            .show()
            .removeClass('hidden-by-filter');

        // Update count for all items
        updateResultsCount($allItems.length, $allItems.length, {});
    }
    
    // Show loading indicator
    function showLoader() {
        $(config.loaderClass).show();
        $(config.queryContainer).addClass('filtering-in-progress');
    }
    
    // Hide loading indicator
    function hideLoader() {
        $(config.loaderClass).hide();
        $(config.queryContainer).removeClass('filtering-in-progress');
    }
    
    // Show error message
    function showError(message) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
        
        if ($looper.length > 0) {
            $looper.html(`<div class="filter-error"><p>${message}</p></div>`);
        }
    }
    
    // Scroll to results
    function scrollToResults() {
        const $queryContainer = $(config.queryContainer);
        if ($queryContainer.length > 0) {
            $('html, body').animate({
                scrollTop: $queryContainer.offset().top - 100
            }, 500);
        }
    }
    
    // Initialize when DOM is ready
    initProjectFilters();
    
    // Re-initialize on dynamic content changes (if needed)
    $(document).on('DOMContentLoaded', initProjectFilters);
});
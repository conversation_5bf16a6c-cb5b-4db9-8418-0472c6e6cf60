jQuery(document).ready(function($) {
    'use strict';
    
    // Configuration
    const config = {
        filterContainer: '.project-filters',
        queryContainer: '.projects-query',
        loaderClass: '.filter-loading',
        postsPerPage: 12,
        currentPage: 1,
        isLoading: false
    };
    
    // Initialize filters
    function initProjectFilters() {
        const $filterContainer = $(config.filterContainer);

        if ($filterContainer.length === 0) {
            return;
        }

        // Get target query container from data attribute
        const targetQuery = $filterContainer.data('target');
        if (targetQuery) {
            config.queryContainer = targetQuery;
        }

        // Bind filter events
        bindFilterEvents();

        // Initialize pagination if exists
        bindPaginationEvents();

        // Show initial count
        showInitialCount();
    }

    // Show initial project count
    function showInitialCount() {
        // Wait a bit for project data to be loaded
        setTimeout(function() {
            const $queryContainer = $(config.queryContainer);
            const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
            const $allItems = $looper.find('.gb-loop-item');

            if ($allItems.length > 0) {
                // Get total count from project data if available
                let totalProjects = $allItems.length;
                if (window.projectFilterData && window.projectFilterData.length > 0) {
                    totalProjects = window.projectFilterData.length;
                }

                updateResultsCount($allItems.length, totalProjects, {});

                // Debug: Log project classes to console
                console.log('Debug: Project items found:', $allItems.length);
                $allItems.each(function(index) {
                    const $item = $(this);
                    console.log(`Project ${index + 1}:`, $item.attr('class'));
                });
            }
        }, 1000); // Increased timeout to ensure project data is loaded
    }
    
    // Bind filter control events
    function bindFilterEvents() {
        const $filterContainer = $(config.filterContainer);
        
        // Filter select changes
        $filterContainer.on('change', '.filter-select', function() {
            config.currentPage = 1; // Reset to first page
            performFilter();
        });
        
        // Clear filters button
        $filterContainer.on('click', '#clear-filters', function(e) {
            e.preventDefault();
            clearAllFilters();
        });
    }
    
    // Bind pagination events
    function bindPaginationEvents() {
        $(document).on('click', '.pagination-link, .filter-page-link', function(e) {
            e.preventDefault();

            if (config.isLoading) {
                return;
            }

            const page = parseInt($(this).data('page'));
            if (page && page > 0) {
                config.currentPage = page;
                performFilter();
            }
        });
    }
    
    // Perform AJAX filtering to get all matching projects from database
    function performFilter() {
        if (config.isLoading) {
            return;
        }

        config.isLoading = true;
        showLoader();

        // Get current filter values
        const filterData = getFilterData();

        // Check if any filters are active
        const hasActiveFilters = filterData.projectYear || filterData.industry;

        if (!hasActiveFilters) {
            // No filters active - reload page to show original pagination
            window.location.href = window.location.pathname;
            return;
        }

        console.log('Performing AJAX filter with data:', filterData);

        // AJAX request to get filtered results from entire database
        $.ajax({
            url: project_filter_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'filter_projects',
                nonce: project_filter_ajax.nonce,
                project_year: filterData.projectYear,
                industry: filterData.industry,
                paged: config.currentPage,
                posts_per_page: config.postsPerPage
            },
            success: function(response) {
                console.log('AJAX response:', response);
                if (response.success) {
                    updateQueryResults(response.data);
                    updatePagination(response.data.pagination);

                    // Scroll to results
                    scrollToResults();
                } else {
                    console.error('Filter request failed:', response);
                    showError('Failed to load filtered results. Please try again.');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                console.error('Response:', xhr.responseText);
                showError('An error occurred while filtering. Please try again.');
            },
            complete: function() {
                config.isLoading = false;
                hideLoader();
            }
        });
    }
    
    // Get current filter values
    function getFilterData() {
        return {
            projectYear: $('#project-year-filter').val() || '',
            industry: $('#industry-filter').val() || ''
        };
    }
    
    // Update query results with AJAX response data
    function updateQueryResults(data) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();

        if ($looper.length === 0) {
            console.error('Query loop container not found');
            return;
        }

        console.log('Updating query results with data:', data);

        // Get total count from AJAX response (filtered results)
        let totalItems = data.pagination ? data.pagination.total_posts : 0;
        let visibleCount = data.posts ? data.posts.length : 0;

        // Clear existing content
        $looper.empty();

        // Add filtered results (HTML already rendered by PHP)
        if (data.posts && data.posts.length > 0) {
            data.posts.forEach(function(projectHtml) {
                // projectHtml is already complete HTML from PHP
                $looper.append(projectHtml);
            });
        } else {
            $looper.append('<div class="no-results"><p>No projects found matching your criteria.</p></div>');
        }

        // Show results count
        const filterData = getFilterData();
        updateResultsCount(visibleCount, totalItems, filterData);

        // Handle pagination visibility
        updatePaginationVisibility(visibleCount);

        // Trigger custom event for other scripts
        $(document).trigger('projectsFiltered', [data, visibleCount]);
    }

    // Update results count display
    function updateResultsCount(visibleCount, totalItems, filterData) {
        const $filterContainer = $(config.filterContainer);

        // Create count message
        let countMessage = '';
        const hasActiveFilters = filterData.projectYear || filterData.industry;

        if (hasActiveFilters) {
            // When filtering, show total filtered results (not pagination-based)
            countMessage = `Showing ${totalItems} projects`;
        } else {
            // When no filters, show current page count of total
            countMessage = `Showing ${visibleCount} of ${totalItems} projects`;
        }

        // Check if count already exists
        let $existingCount = $('.filter-results-count');

        if ($existingCount.length > 0) {
            // Update existing count with animation
            $existingCount.addClass('updated');
            $existingCount.text(countMessage);

            // Remove updated class after animation
            setTimeout(function() {
                $existingCount.removeClass('updated');
            }, 500);
        } else {
            // Add new count display after filter controls
            const countHtml = `<div class="filter-results-count">${countMessage}</div>`;
            $filterContainer.append(countHtml);
        }
    }

    // Update pagination visibility based on filtered results
    function updatePaginationVisibility(visibleCount) {
        const $pagination = $('.gb-query-loop-pagination-projects, .gb-element-05a39879, [class*="gb-query-loop-pagination"]');

        if ($pagination.length === 0) {
            return;
        }

        // Hide pagination if filtered results are less than posts per page
        // or if there are very few results (6 or less)
        const shouldHidePagination = visibleCount <= config.postsPerPage || visibleCount <= 6;

        if (shouldHidePagination) {
            $pagination.hide().addClass('hidden-by-filter');
        } else {
            $pagination.show().removeClass('hidden-by-filter');
        }
    }
    
    // Update pagination for filtered results
    function updatePagination(paginationData) {
        // Remove existing pagination
        $('.gb-query-loop-pagination-projects, .gb-element-05a39879, [class*="gb-query-loop-pagination"]').remove();

        if (paginationData.total_pages > 1) {
            const paginationHtml = generateFilterPaginationHtml(paginationData);
            $(config.queryContainer).after(paginationHtml);
        }
    }

    // Generate pagination HTML for filtered results
    function generateFilterPaginationHtml(pagination) {
        let html = '<div class="gb-element-05a39879 gb-query-loop-pagination-projects filter-pagination">';
        html += '<nav class="gb-query-page-numbers-6318665e">';

        // Page numbers
        for (let i = 1; i <= pagination.total_pages; i++) {
            if (i === pagination.current_page) {
                html += `<span aria-current="page" class="page-numbers current">${i}</span>`;
            } else {
                html += `<a class="page-numbers filter-page-link" href="#" data-page="${i}">${i}</a>`;
            }
        }

        html += '</nav>';

        // Next button
        if (pagination.current_page < pagination.total_pages) {
            html += `<a class="general-border gb-text-ec21522c filter-page-link" href="#" data-page="${pagination.current_page + 1}">
                <span class="gb-shape">
                    <svg aria-hidden="true" role="img" height="1em" width="1em" viewBox="0 0 256 512" xmlns="http://www.w3.org/2000/svg">
                        <path fill="currentColor" d="M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z"></path>
                    </svg>
                </span>
            </a>`;
        }

        html += '</div>';
        return html;
    }
    
    // Clear all filters
    function clearAllFilters() {
        $('#project-year-filter').val('');
        $('#industry-filter').val('');
        config.currentPage = 1;

        // Remove filter results count
        $('.filter-results-count').remove();

        // Reload page to show original pagination and all projects
        window.location.href = window.location.pathname;
    }
    
    // Show loading indicator
    function showLoader() {
        $(config.loaderClass).show();
        $(config.queryContainer).addClass('filtering-in-progress');
    }
    
    // Hide loading indicator
    function hideLoader() {
        $(config.loaderClass).hide();
        $(config.queryContainer).removeClass('filtering-in-progress');
    }
    
    // Show error message
    function showError(message) {
        const $queryContainer = $(config.queryContainer);
        const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
        
        if ($looper.length > 0) {
            $looper.html(`<div class="filter-error"><p>${message}</p></div>`);
        }
    }
    
    // Scroll to results
    function scrollToResults() {
        const $queryContainer = $(config.queryContainer);
        if ($queryContainer.length > 0) {
            $('html, body').animate({
                scrollTop: $queryContainer.offset().top - 100
            }, 500);
        }
    }
    
    // Initialize when DOM is ready
    initProjectFilters();
    
    // Re-initialize on dynamic content changes (if needed)
    $(document).on('DOMContentLoaded', initProjectFilters);
});
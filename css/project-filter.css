/* Project Filter Styles */

.project-filters {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-label {
    font-weight: 600;
    color: #495057;
    margin-right: 0.5rem;
}

.filter-group {
    display: flex;
    align-items: center;
}

.filter-select {
    padding: 0.5rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
    font-size: 0.9rem;
    color: #495057;
    min-width: 150px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-select:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.clear-filters-btn {
    padding: 0.5rem 1rem;
    background-color: #6c757d;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.clear-filters-btn:hover {
    background-color: #5a6268;
}

.clear-filters-btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.filter-loading {
    text-align: center;
    padding: 1rem;
    color: #6c757d;
    font-style: italic;
}

/* Query container loading state */
.filtering-in-progress {
    opacity: 0.6;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* Project card styles to match the design */
.project-card {
    padding: 1.5rem;
    background: #fff;
    border-radius: 8px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.project-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #212529;
}

.project-status {
    color: #28a745;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.project-status::before {
    content: "✓";
    margin-right: 0.5rem;
    font-weight: bold;
}

.project-field {
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.project-field strong {
    color: #495057;
    display: inline-block;
    min-width: 80px;
}

.project-field ul {
    margin: 0.25rem 0 0 0;
    padding-left: 1.2rem;
}

.project-field li {
    margin-bottom: 0.25rem;
    color: #6c757d;
}

/* Pagination styles */
.project-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
    padding: 1rem;
}

.pagination-link,
.pagination-current {
    padding: 0.5rem 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-decoration: none;
    color: #495057;
    background-color: #fff;
    transition: all 0.15s ease-in-out;
}

.pagination-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    text-decoration: none;
}

.pagination-current {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
    font-weight: 600;
}

/* Error and no results states */
.filter-error,
.no-results {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 1rem 0;
}

.filter-error p,
.no-results p {
    margin: 0;
    font-size: 1.1rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .filter-label {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .filter-group {
        justify-content: stretch;
    }
    
    .filter-select {
        min-width: 100%;
    }
    
    .clear-filters-btn {
        width: 100%;
    }
    
    .project-pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .pagination-link,
    .pagination-current {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .project-filters {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .project-card {
        padding: 1rem;
    }
    
    .project-title {
        font-size: 1rem;
    }
    
    .project-field {
        font-size: 0.85rem;
    }
}

/* Animation for smooth transitions */
.gb-looper-f431c559,
[class*="gb-looper-"] {
    transition: opacity 0.3s ease;
}

/* Loading animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.filtering-in-progress .gb-loop-item {
    animation: pulse 1.5s ease-in-out infinite;
}

// Debug script untuk troubleshooting project filters
// Tambahkan ini ke browser console untuk debugging

function debugProjectFilters() {
    console.log('=== PROJECT FILTER DEBUG ===');
    
    // 1. Check if project data is loaded
    console.log('1. Project Data Check:');
    if (window.projectFilterData) {
        console.log('✅ Project data loaded:', window.projectFilterData.length, 'projects');
        window.projectFilterData.forEach((project, index) => {
            console.log(`   ${index + 1}. ${project.title} - Classes: ${project.classes}`);
        });
    } else {
        console.log('❌ Project data not loaded');
    }
    
    // 2. Check DOM elements
    console.log('\n2. DOM Elements Check:');
    const $queryContainer = $('.projects-query');
    console.log('Query container found:', $queryContainer.length);
    
    const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
    console.log('Looper container found:', $looper.length);
    
    const $allItems = $looper.find('.gb-loop-item');
    console.log('Project items found:', $allItems.length);
    
    // 3. Check project item classes
    console.log('\n3. Project Item Classes:');
    $allItems.each(function(index) {
        const $item = $(this);
        const title = $item.find('h1, h2, h3, h4, h5, h6, .project-title, .gb-headline, .wp-block-heading, [class*="gb-headline"]').first().text().trim();
        console.log(`   ${index + 1}. "${title}" - Classes: ${$item.attr('class')}`);
    });
    
    // 4. Check filter controls
    console.log('\n4. Filter Controls Check:');
    const $yearFilter = $('#project-year-filter');
    const $industryFilter = $('#industry-filter');
    console.log('Year filter found:', $yearFilter.length);
    console.log('Industry filter found:', $industryFilter.length);
    
    if ($yearFilter.length > 0) {
        console.log('Year filter options:');
        $yearFilter.find('option').each(function() {
            const $option = $(this);
            console.log(`   - ${$option.val()}: ${$option.text()}`);
        });
    }
    
    if ($industryFilter.length > 0) {
        console.log('Industry filter options:');
        $industryFilter.find('option').each(function() {
            const $option = $(this);
            console.log(`   - ${$option.val()}: ${$option.text()}`);
        });
    }
    
    // 5. Check taxonomies
    console.log('\n5. Taxonomy Check:');
    if (window.projectFilterData) {
        const allYears = new Set();
        const allIndustries = new Set();
        
        window.projectFilterData.forEach(project => {
            const classes = project.classes.split(' ');
            classes.forEach(cls => {
                if (cls.startsWith('project-year-')) {
                    allYears.add(cls.replace('project-year-', ''));
                }
                if (cls.startsWith('industry-')) {
                    allIndustries.add(cls.replace('industry-', ''));
                }
            });
        });
        
        console.log('Years found in data:', Array.from(allYears));
        console.log('Industries found in data:', Array.from(allIndustries));
    }
    
    console.log('\n=== END DEBUG ===');
}

// Test specific filter
function testFilter(year, industry) {
    console.log(`\n=== TESTING FILTER: Year=${year}, Industry=${industry} ===`);
    
    const $allItems = $('.gb-loop-item');
    let matchCount = 0;
    
    $allItems.each(function() {
        const $item = $(this);
        let shouldShow = true;
        
        if (year) {
            const yearClass = 'project-year-' + year;
            if (!$item.hasClass(yearClass)) {
                shouldShow = false;
            }
        }
        
        if (industry) {
            const industryClass = 'industry-' + industry;
            if (!$item.hasClass(industryClass)) {
                shouldShow = false;
            }
        }
        
        if (shouldShow) {
            matchCount++;
            const title = $item.find('h1, h2, h3, h4, h5, h6, .project-title, .gb-headline, .wp-block-heading, [class*="gb-headline"]').first().text().trim();
            console.log(`✅ Match: "${title}" - Classes: ${$item.attr('class')}`);
        }
    });
    
    console.log(`Total matches: ${matchCount}`);
    console.log('=== END TEST ===');
}

// Auto-run debug when script loads
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function() {
        setTimeout(debugProjectFilters, 2000);
    });
}

console.log('Debug functions loaded. Run debugProjectFilters() or testFilter("2021", "water-treatment") in console.');

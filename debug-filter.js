// Debug script untuk troubleshooting project filters
// Tambahkan ini ke browser console untuk debugging

function debugProjectFilters() {
    console.log('=== PROJECT FILTER DEBUG ===');
    
    // 1. Check if project data is loaded
    console.log('1. Project Data Check:');
    if (window.projectFilterData) {
        console.log('✅ Project data loaded:', window.projectFilterData.length, 'projects');
        window.projectFilterData.forEach((project, index) => {
            console.log(`   ${index + 1}. ${project.title} - Classes: ${project.classes}`);
        });
    } else {
        console.log('❌ Project data not loaded');
    }
    
    // 2. Check DOM elements
    console.log('\n2. DOM Elements Check:');
    const $queryContainer = $('.projects-query');
    console.log('Query container found:', $queryContainer.length);
    
    const $looper = $queryContainer.find('.gb-looper-f431c559, [class*="gb-looper-"]').first();
    console.log('Looper container found:', $looper.length);
    
    const $allItems = $looper.find('.gb-loop-item');
    console.log('Project items found:', $allItems.length);
    
    // 3. Check project item classes
    console.log('\n3. Project Item Classes:');
    $allItems.each(function(index) {
        const $item = $(this);
        const title = $item.find('h1, h2, h3, h4, h5, h6, .project-title, .gb-headline, .wp-block-heading, [class*="gb-headline"]').first().text().trim();
        console.log(`   ${index + 1}. "${title}" - Classes: ${$item.attr('class')}`);
    });
    
    // 4. Check filter controls
    console.log('\n4. Filter Controls Check:');
    const $yearFilter = $('#project-year-filter');
    const $industryFilter = $('#industry-filter');
    console.log('Year filter found:', $yearFilter.length);
    console.log('Industry filter found:', $industryFilter.length);
    
    if ($yearFilter.length > 0) {
        console.log('Year filter options:');
        $yearFilter.find('option').each(function() {
            const $option = $(this);
            console.log(`   - ${$option.val()}: ${$option.text()}`);
        });
    }
    
    if ($industryFilter.length > 0) {
        console.log('Industry filter options:');
        $industryFilter.find('option').each(function() {
            const $option = $(this);
            console.log(`   - ${$option.val()}: ${$option.text()}`);
        });
    }
    
    // 5. Check taxonomies from DOM classes
    console.log('\n5. Taxonomy Check from DOM:');
    const allYearClasses = new Set();
    const allIndustryClasses = new Set();

    $allItems.each(function() {
        const $item = $(this);
        const classes = $item.attr('class').split(' ');
        classes.forEach(cls => {
            if (cls.startsWith('project-year-')) {
                allYearClasses.add(cls);
            }
            if (cls.startsWith('industry-')) {
                allIndustryClasses.add(cls);
            }
        });
    });

    console.log('Year classes found in DOM:', Array.from(allYearClasses));
    console.log('Industry classes found in DOM:', Array.from(allIndustryClasses));

    // 6. Check mappings
    console.log('\n6. Mapping Check:');
    if (window.yearMapping) {
        console.log('Year mapping available:', window.yearMapping);
    }
    if (window.industryMapping) {
        console.log('Industry mapping available:', window.industryMapping);
    }
    
    console.log('\n=== END DEBUG ===');
}

// Test specific filter using correct mapping
function testFilter(year, industry) {
    console.log(`\n=== TESTING FILTER: Year=${year}, Industry=${industry} ===`);

    const $allItems = $('.gb-loop-item');
    let matchCount = 0;

    $allItems.each(function() {
        const $item = $(this);
        let shouldShow = true;

        if (year) {
            let yearClass = '';

            // Use mapping if available
            if (window.yearMapping && window.yearMapping[year]) {
                yearClass = 'project-year-' + window.yearMapping[year];
            } else {
                yearClass = 'project-year-' + year;
            }

            console.log(`Looking for year class: ${yearClass}`);
            if (!$item.hasClass(yearClass)) {
                shouldShow = false;
            }
        }

        if (industry) {
            const industryClass = 'industry-' + industry;
            console.log(`Looking for industry class: ${industryClass}`);
            if (!$item.hasClass(industryClass)) {
                shouldShow = false;
            }
        }

        if (shouldShow) {
            matchCount++;
            const title = $item.find('h1, h2, h3, h4, h5, h6, .project-title, .gb-headline, .wp-block-heading, [class*="gb-headline"]').first().text().trim();
            console.log(`✅ Match: "${title}" - Classes: ${$item.attr('class')}`);
        }
    });

    console.log(`Total matches: ${matchCount}`);
    console.log('=== END TEST ===');
}

// Test with actual class names from DOM
function testFilterByClass(yearClass, industryClass) {
    console.log(`\n=== TESTING BY CLASS: ${yearClass}, ${industryClass} ===`);

    const $allItems = $('.gb-loop-item');
    let matchCount = 0;

    $allItems.each(function() {
        const $item = $(this);
        let shouldShow = true;

        if (yearClass && !$item.hasClass(yearClass)) {
            shouldShow = false;
        }

        if (industryClass && !$item.hasClass(industryClass)) {
            shouldShow = false;
        }

        if (shouldShow) {
            matchCount++;
            console.log(`✅ Match: Classes: ${$item.attr('class')}`);
        }
    });

    console.log(`Total matches: ${matchCount}`);
    console.log('=== END TEST ===');
}

// Auto-run debug when script loads
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function() {
        setTimeout(debugProjectFilters, 2000);
    });
}

console.log('Debug functions loaded. Run debugProjectFilters() or testFilter("2021", "water-treatment") in console.');

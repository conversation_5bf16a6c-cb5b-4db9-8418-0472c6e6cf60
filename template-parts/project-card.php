<?php
/**
 * Template part for displaying project cards
 * This template matches EXACTLY the HTML structure from Query Loop
 */

// Get ACF fields
$end_user = get_field('end-user');
$location = get_field('location');
$scope = get_field('scope');

// Get taxonomy terms
$year_terms = get_the_terms(get_the_ID(), 'project-year');
$year_display = '';
if ($year_terms && !is_wp_error($year_terms)) {
    $year_display = $year_terms[0]->name;
}
?>

<!-- Project Title and Status Section -->
<div class="gb-element-b96d08d3">
    <p class="gb-text gb-text-133de045 title-project"><?php the_title(); ?></p>

    <p class="text-semibold gb-text-a5c55edf">
        <span class="gb-shape">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--ic" width="16" height="16" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24">
                <path fill="currentColor" d="M9 16.17L5.53 12.7a.996.996 0 1 0-1.41 1.41l4.18 4.18c.39.39 1.02.39 1.41 0L20.29 7.71a.996.996 0 1 0-1.41-1.41z"></path>
            </svg>
        </span>
        <span class="gb-text">Completed</span>
    </p>
</div>

<!-- End User Section -->
<?php if ($end_user): ?>
<div class="gb-element-28e360ca">
    <p class="gb-text text-medium gb-text-dc1dbbfe">End User :</p>
    <p class="gb-text text-semibold gb-text-9bbcd125"><?php echo esc_html($end_user); ?></p>
</div>
<?php endif; ?>

<!-- Location Section -->
<?php if ($location): ?>
<div class="gb-element-19c441d9">
    <p class="gb-text text-medium gb-text-83c8f794">Location :</p>
    <p class="gb-text text-semibold gb-text-63bdb25b"><?php echo esc_html($location); ?></p>
</div>
<?php endif; ?>

<!-- Year Section -->
<?php if ($year_display): ?>
<div class="gb-element-e5d8346c">
    <p class="gb-text text-medium gb-text-c2aaa29c">Year:</p>
    <p class="gb-text text-semibold gb-text-ebdb7ec4"><span><?php echo esc_html($year_display); ?></span></p>
</div>
<?php endif; ?>

<!-- Scope Section -->
<?php if ($scope && is_array($scope) && !empty($scope)): ?>
<div class="gb-element-764c8652 scope-list">
    <p class="gb-text text-medium gb-text-8a907d4c">Scope:</p>

    <div class="gb-element-a4c54532">
        <?php foreach ($scope as $scope_item): ?>
            <li class="gb-text text-semibold"><?php echo esc_html($scope_item); ?></li>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

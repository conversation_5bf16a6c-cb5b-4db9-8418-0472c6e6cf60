<?php
/**
 * Template part for displaying project cards
 * This template ensures consistent HTML structure between Query Loop and AJAX filtering
 */

// Get ACF fields
$end_user = get_field('end-user');
$location = get_field('location');
$scope = get_field('scope');

// Get taxonomy terms
$year_terms = get_the_terms(get_the_ID(), 'project-year');
$year_display = '';
if ($year_terms && !is_wp_error($year_terms)) {
    $year_display = $year_terms[0]->name;
}
?>

<div class="gb-container gb-container-4e17580f">
    <div class="gb-inside-container">
        
        <!-- Project Title and Status -->
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1.5rem;">
            <h3 style="margin: 0; font-size: 1.875rem; font-weight: 600; line-height: 1.2; color: #1a202c;">
                <?php the_title(); ?>
            </h3>
            <div style="display: flex; align-items: center; color: #38a169; font-size: 1rem; margin-left: 1rem;">
                <span style="margin-right: 0.5rem;">✓</span>
                <span>Completed</span>
            </div>
        </div>
        
        <!-- Project Details -->
        <?php if ($end_user): ?>
            <div style="margin-bottom: 1rem; font-size: 1.125rem; color: #4a5568;">
                <span style="color: #2d3748; font-weight: 500;">End User :</span>
                <span style="margin-left: 0.5rem;"><?php echo esc_html($end_user); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if ($location): ?>
            <div style="margin-bottom: 1rem; font-size: 1.125rem; color: #4a5568;">
                <span style="color: #2d3748; font-weight: 500;">Location :</span>
                <span style="margin-left: 0.5rem;"><?php echo esc_html($location); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if ($year_display): ?>
            <div style="margin-bottom: 1rem; font-size: 1.125rem; color: #4a5568;">
                <span style="color: #2d3748; font-weight: 500;">Year :</span>
                <span style="margin-left: 0.5rem;"><?php echo esc_html($year_display); ?></span>
            </div>
        <?php endif; ?>
        
        <?php if ($scope && is_array($scope) && !empty($scope)): ?>
            <div style="margin-bottom: 1rem;">
                <div style="color: #2d3748; font-weight: 500; margin-bottom: 0.75rem; font-size: 1.125rem;">Scope:</div>
                <ul style="margin: 0; padding-left: 1.5rem; list-style-type: disc;">
                    <?php foreach ($scope as $scope_item): ?>
                        <li style="margin-bottom: 0.5rem; color: #2d3748; font-size: 1rem;"><?php echo esc_html($scope_item); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
    </div>
</div>

<?php
/**
 * Template part for displaying project cards
 * This template matches EXACTLY the HTML structure from Query Loop
 */

// Get ACF fields with correct field names
$end_user = get_field('end_user');
$location = get_field('location');
$year = get_field('year');
$status = get_field('status');

// Get repeater fields
$scopes = get_field('scopes'); // Parent repeater field
$technologies = get_field('technologies'); // Parent repeater field

// Get taxonomy terms (fallback if ACF year field is empty)
$year_terms = get_the_terms(get_the_ID(), 'project-year');
$year_display = $year; // Use ACF field first
if (empty($year_display) && $year_terms && !is_wp_error($year_terms)) {
    $year_display = $year_terms[0]->name;
}

// Status display (use ACF field or default to "Completed")
$status_display = !empty($status) ? $status : 'Completed';
?>

<!-- Project Title and Status Section -->
<div class="gb-element-b96d08d3">
    <p class="gb-text gb-text-133de045 title-project"><?php the_title(); ?></p>

    <p class="text-semibold gb-text-a5c55edf">
        <span class="gb-shape">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--ic" width="16" height="16" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24">
                <path fill="currentColor" d="M9 16.17L5.53 12.7a.996.996 0 1 0-1.41 1.41l4.18 4.18c.39.39 1.02.39 1.41 0L20.29 7.71a.996.996 0 1 0-1.41-1.41z"></path>
            </svg>
        </span>
        <span class="gb-text"><?php echo esc_html($status_display); ?></span>
    </p>
</div>

<!-- End User Section -->
<?php if ($end_user): ?>
<div class="gb-element-28e360ca">
    <p class="gb-text text-medium gb-text-dc1dbbfe">End User :</p>
    <p class="gb-text text-semibold gb-text-9bbcd125"><?php echo esc_html($end_user); ?></p>
</div>
<?php endif; ?>

<!-- Location Section -->
<?php if ($location): ?>
<div class="gb-element-19c441d9">
    <p class="gb-text text-medium gb-text-83c8f794">Location :</p>
    <p class="gb-text text-semibold gb-text-63bdb25b"><?php echo esc_html($location); ?></p>
</div>
<?php endif; ?>

<!-- Year Section -->
<?php if ($year_display): ?>
<div class="gb-element-e5d8346c">
    <p class="gb-text text-medium gb-text-c2aaa29c">Year:</p>
    <p class="gb-text text-semibold gb-text-ebdb7ec4"><span><?php echo esc_html($year_display); ?></span></p>
</div>
<?php endif; ?>

<!-- Scope Section -->
<?php if ($scopes && is_array($scopes) && !empty($scopes)): ?>
<div class="gb-element-764c8652 scope-list">
    <p class="gb-text text-medium gb-text-8a907d4c">Scope:</p>

    <div class="gb-element-a4c54532">
        <?php
        foreach ($scopes as $index => $scope_row):
            $scope_item = $scope_row['item']; // Get the 'item' sub-field from repeater
            $is_hidden = $index >= 2; // Hide items after the first 2
            $is_last_visible = $index == 1 && count($scopes) > 2; // Show "+more" on second item if there are more
        ?>
            <li class="gb-text text-semibold"<?php echo $is_hidden ? ' style="display: none;"' : ''; ?>>
                <?php echo esc_html($scope_item); ?>
                <?php if ($is_last_visible): ?>
                    <span class="more-indicator" style="color: var(--brand); font-weight: normal; cursor: pointer; display: block;"> +<?php echo count($scopes) - 2; ?> more</span>
                <?php endif; ?>
            </li>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<!-- Technologies Section -->
<?php if ($technologies && is_array($technologies) && !empty($technologies)): ?>
<div class="technologies-list">
    <p class="gb-text text-medium gb-text-73e40212">Technologies:</p>

    <div>
        <?php
        foreach ($technologies as $index => $tech_row):
            $tech_item = $tech_row['item']; // Get the 'item' sub-field from repeater
            $is_hidden = $index >= 2; // Hide items after the first 2
            $tech_classes = array('gb-text', 'text-semibold', 'button-tech');

            // Generate unique class for each tech button (mimicking the original structure)
            $unique_classes = array('gb-text-75a39d79', 'gb-text-82d85188', 'gb-text-2601aac9');
            if (isset($unique_classes[$index])) {
                $tech_classes[] = $unique_classes[$index];
            }
        ?>
            <button class="<?php echo esc_attr(implode(' ', $tech_classes)); ?>"<?php echo $is_hidden ? ' style="display: none;"' : ''; ?>>
                <?php echo esc_html($tech_item); ?>
            </button>
        <?php endforeach; ?>

        <?php if (count($technologies) > 2): ?>
            <button class="gb-text text-semibold button-tech more-tech-btn" style="background-color: transparent; color: var(--brand);">
                +<?php echo count($technologies) - 2; ?> more
            </button>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

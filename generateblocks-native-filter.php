<?php
/**
 * Native GenerateBlocks Query Loop Filtering
 * Much simpler than custom AJAX solution!
 * Based on official GenerateBlocks support solution
 */

// Filter GenerateBlocks Query Loop using native hook
add_filter( 'generateblocks_query_loop_args', 'native_project_filter', 10, 2 );

function native_project_filter( $query_args, $attributes ) {
    // Target specific Query Loop by CSS class
    $target_class = 'projects-query-loop'; // Add this class to your Query Loop Grid
    
    // Only apply to our specific Query Loop
    if ( empty( $attributes['className'] ) || 
         strpos( $attributes['className'], $target_class ) === false ) {
        return $query_args;
    }
    
    // Get filter parameters from URL or form submission
    $year_filter = $_GET['project_year'] ?? '';
    $industry_filter = $_GET['industry'] ?? '';
    
    // Build tax_query array
    $tax_query = array();
    
    if ( !empty( $year_filter ) ) {
        $tax_query[] = array(
            'taxonomy' => 'project-year',
            'field'    => 'slug',
            'terms'    => sanitize_text_field( $year_filter )
        );
    }
    
    if ( !empty( $industry_filter ) ) {
        $tax_query[] = array(
            'taxonomy' => 'industry', 
            'field'    => 'slug',
            'terms'    => sanitize_text_field( $industry_filter )
        );
    }
    
    // Apply filters if any exist
    if ( !empty( $tax_query ) ) {
        if ( count( $tax_query ) > 1 ) {
            $tax_query['relation'] = 'AND';
        }
        
        $query_args['tax_query'] = $tax_query;
        
        // Add sorting by year (newest first)
        $query_args['orderby'] = 'date';
        $query_args['order'] = 'DESC';
    }
    
    return $query_args;
}

// Simple form for filtering (no AJAX needed)
function render_simple_project_filter() {
    $current_year = $_GET['project_year'] ?? '';
    $current_industry = $_GET['industry'] ?? '';
    
    ?>
    <form method="GET" class="simple-project-filter">
        <select name="project_year">
            <option value="">All Years</option>
            <?php
            $years = get_terms(array(
                'taxonomy' => 'project-year',
                'hide_empty' => true
            ));
            foreach ($years as $year) {
                $selected = ($current_year === $year->slug) ? 'selected' : '';
                echo "<option value='{$year->slug}' {$selected}>{$year->name}</option>";
            }
            ?>
        </select>
        
        <select name="industry">
            <option value="">All Industries</option>
            <?php
            $industries = get_terms(array(
                'taxonomy' => 'industry',
                'hide_empty' => true
            ));
            foreach ($industries as $industry) {
                $selected = ($current_industry === $industry->slug) ? 'selected' : '';
                echo "<option value='{$industry->slug}' {$selected}>{$industry->name}</option>";
            }
            ?>
        </select>
        
        <button type="submit">Filter</button>
        <a href="<?php echo remove_query_arg(array('project_year', 'industry')); ?>">Clear</a>
    </form>
    <?php
}

// Shortcode to display filter form
add_shortcode('project_filter', 'render_simple_project_filter');

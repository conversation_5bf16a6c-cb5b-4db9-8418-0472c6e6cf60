// Test dengan data aktual dari debug
console.log('=== TESTING WITH ACTUAL DATA ===');

// Berdasarkan debug output, kita punya:
// Year mapping: {2021: 25, 2020: 26, ...}
// Industry classes: industry-water-wastewater, industry-oil-gas-industry, dll

function testActualData() {
    console.log('\n1. Testing Year 2021 (should map to project-year-25):');
    
    const $allItems = $('.gb-loop-item');
    console.log('Total items in DOM:', $allItems.length);
    
    // Test year 2021 -> project-year-25
    let year2021Count = 0;
    $allItems.each(function() {
        const $item = $(this);
        if ($item.hasClass('project-year-25')) {
            year2021Count++;
            console.log(`✅ Year 2021 match: ${$item.attr('class')}`);
        }
    });
    console.log(`Total Year 2021 matches: ${year2021Count}`);
    
    console.log('\n2. Testing Year 2020 (should map to project-year-26):');
    let year2020Count = 0;
    $allItems.each(function() {
        const $item = $(this);
        if ($item.hasClass('project-year-26')) {
            year2020Count++;
            console.log(`✅ Year 2020 match: ${$item.attr('class')}`);
        }
    });
    console.log(`Total Year 2020 matches: ${year2020Count}`);
    
    console.log('\n3. Testing Industry Water & Wastewater:');
    let waterCount = 0;
    $allItems.each(function() {
        const $item = $(this);
        if ($item.hasClass('industry-water-wastewater')) {
            waterCount++;
            console.log(`✅ Water industry match: ${$item.attr('class')}`);
        }
    });
    console.log(`Total Water industry matches: ${waterCount}`);
    
    console.log('\n4. Testing Industry Oil & Gas:');
    let oilCount = 0;
    $allItems.each(function() {
        const $item = $(this);
        if ($item.hasClass('industry-oil-gas-industry')) {
            oilCount++;
            console.log(`✅ Oil & Gas match: ${$item.attr('class')}`);
        }
    });
    console.log(`Total Oil & Gas matches: ${oilCount}`);
    
    console.log('\n5. Testing Combined: Year 2021 + Water Industry:');
    let combinedCount = 0;
    $allItems.each(function() {
        const $item = $(this);
        if ($item.hasClass('project-year-25') && $item.hasClass('industry-water-wastewater')) {
            combinedCount++;
            console.log(`✅ Combined match: ${$item.attr('class')}`);
        }
    });
    console.log(`Total Combined matches: ${combinedCount}`);
}

// Test filter function dengan data yang benar
function testFilterWithCorrectData() {
    console.log('\n=== TESTING FILTER FUNCTION ===');
    
    // Simulate filter data
    const filterData = {
        projectYear: "2021",
        industry: ""
    };
    
    console.log('Filter data:', filterData);
    
    const $allItems = $('.gb-loop-item');
    let visibleCount = 0;
    
    $allItems.each(function() {
        const $item = $(this);
        let shouldShow = true;
        
        // Check year filter dengan mapping
        if (filterData.projectYear) {
            let yearClass = '';
            
            // Use mapping if available
            if (window.yearMapping && window.yearMapping[filterData.projectYear]) {
                yearClass = 'project-year-' + window.yearMapping[filterData.projectYear];
            } else {
                yearClass = 'project-year-' + filterData.projectYear;
            }
            
            console.log(`Looking for year class: ${yearClass}`);
            if (!$item.hasClass(yearClass)) {
                shouldShow = false;
            }
        }
        
        // Check industry filter
        if (filterData.industry) {
            const industryClass = 'industry-' + filterData.industry;
            console.log(`Looking for industry class: ${industryClass}`);
            if (!$item.hasClass(industryClass)) {
                shouldShow = false;
            }
        }
        
        if (shouldShow) {
            visibleCount++;
            console.log(`✅ Would show: ${$item.attr('class')}`);
        }
    });
    
    console.log(`Total items that would be visible: ${visibleCount}`);
}

// Test industry mapping issue
function testIndustryMapping() {
    console.log('\n=== TESTING INDUSTRY MAPPING ===');
    
    if (window.industryMapping) {
        console.log('Current industry mapping:');
        Object.keys(window.industryMapping).forEach(key => {
            console.log(`"${key}" -> "${window.industryMapping[key]}"`);
        });
        
        // Test if we can find the correct mapping
        const waterKey = Object.keys(window.industryMapping).find(key => 
            key.includes('Water') || key.includes('Wastewater')
        );
        
        if (waterKey) {
            console.log(`Found water key: "${waterKey}" -> "${window.industryMapping[waterKey]}"`);
        }
    }
}

// Run all tests
testActualData();
testFilterWithCorrectData();
testIndustryMapping();

console.log('\n=== END TESTING ===');

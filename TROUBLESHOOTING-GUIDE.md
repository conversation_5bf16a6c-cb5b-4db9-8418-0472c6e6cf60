# Project Filter Troubleshooting Guide

## 🔍 Masalah yang Teridentifikasi

### 1. Filter by Year Tidak Menampilkan Hasil
**Kemungkinan Penyebab:**
- CSS classes tidak ditambahkan ke project items
- Nama taxonomy atau slug tidak cocok
- Project titles tidak cocok dengan data

### 2. Count Menampilkan "12 projects" bukan Total
**Penyebab:**
- Hanya menghitung items di halaman pertama pagination
- Perlu menghitung dari total data, bukan DOM elements

## 🛠️ Langkah Debugging

### Step 1: Buka Browser Console
1. Buka halaman projects di browser
2. Tekan F12 atau klik kanan → Inspect
3. Buka tab "Console"

### Step 2: Jalankan Debug Function
Ketik di console:
```javascript
debugProjectFilters()
```

### Step 3: Analisis Output
Debug akan menampilkan:
- ✅ Project data loaded: X projects
- ✅ Query container found: 1
- ✅ Project items found: X
- Daftar project titles dan classes
- Filter options yang tersedia
- Taxonomy data

### Step 4: Test Filter Spesifik
Untuk test filter tertentu:
```javascript
testFilter("2021", null)  // Test year 2021 saja
testFilter(null, "water-treatment")  // Test industry saja
testFilter("2021", "water-treatment")  // Test kombinasi
```

## 🔧 Kemungkinan Solusi

### Solusi 1: Periksa Project Titles
Jika project titles tidak cocok:
1. Lihat output debug untuk "Project Item Classes"
2. Bandingkan dengan "Project data loaded"
3. Pastikan titles persis sama (case-sensitive)

### Solusi 2: Periksa Taxonomy Slugs
Jika taxonomy tidak cocok:
1. Cek di WordPress Admin → Projects → Project Year
2. Cek di WordPress Admin → Projects → Industry
3. Pastikan slugs cocok dengan yang di filter options

### Solusi 3: Periksa CSS Selector
Jika project titles tidak terdeteksi:
1. Inspect element project card
2. Cari selector yang tepat untuk title
3. Update di functions.php bagian:
```php
const $titleElement = $item.find('h1, h2, h3, h4, h5, h6, .project-title, .gb-headline, .wp-block-heading, [class*="gb-headline"]').first();
```

## 📋 Checklist Verifikasi

### ✅ Data Setup
- [ ] Custom post type "projects" exists
- [ ] Taxonomy "project-year" exists  
- [ ] Taxonomy "industry" exists
- [ ] Projects have taxonomy terms assigned
- [ ] ACF fields are set up

### ✅ Code Implementation
- [ ] Shortcode `[project_filters]` added to page
- [ ] Query Loop has CSS class "projects-query"
- [ ] JavaScript files loading without errors
- [ ] CSS files loading correctly

### ✅ Browser Testing
- [ ] No JavaScript errors in console
- [ ] Project data appears in debug output
- [ ] Filter controls appear on page
- [ ] CSS classes added to project items

## 🚨 Common Issues & Fixes

### Issue: "Project data not loaded"
**Fix:** 
- Check if projects exist and are published
- Verify taxonomy terms are assigned
- Ensure shortcode is on the same page as Query Loop

### Issue: "Query container not found"
**Fix:**
- Add CSS class "projects-query" to Query Loop container
- Or update shortcode: `[project_filters target_query=".your-class"]`

### Issue: "No project items found"
**Fix:**
- Verify Query Loop is displaying projects
- Check CSS selector for `.gb-loop-item`
- Inspect actual HTML structure

### Issue: Filter options empty
**Fix:**
- Check taxonomy terms exist in WordPress admin
- Verify terms are assigned to published projects
- Check `hide_empty => true` setting

## 🔄 Quick Fixes to Try

### Fix 1: Clear Cache
```php
// Add to functions.php temporarily
wp_cache_flush();
```

### Fix 2: Force Refresh Assets
Change version numbers in functions.php:
```php
'1.0.1' → '1.0.2'
```

### Fix 3: Manual Class Addition
Add to browser console:
```javascript
$('.gb-loop-item').each(function() {
    $(this).addClass('project-year-2021 industry-water-treatment');
});
```

## 📞 Next Steps

1. **Run Debug First**: Always start with `debugProjectFilters()`
2. **Check Console Errors**: Look for red errors in console
3. **Verify Data**: Ensure project data is loaded correctly
4. **Test Manually**: Use `testFilter()` to isolate issues
5. **Check HTML Structure**: Inspect actual DOM elements

## 🎯 Expected Debug Output

**Successful Setup Should Show:**
```
=== PROJECT FILTER DEBUG ===
1. Project Data Check:
✅ Project data loaded: 24 projects
   1. Biogas Power Plant - Classes: project-year-2021 industry-energy
   2. Water Treatment Plan - Classes: project-year-2021 industry-water-treatment
   ...

2. DOM Elements Check:
Query container found: 1
Looper container found: 1
Project items found: 12

3. Project Item Classes:
   1. "Biogas Power Plant" - Classes: gb-loop-item project-year-2021 industry-energy
   2. "Water Treatment Plan" - Classes: gb-loop-item project-year-2021 industry-water-treatment
   ...
```

Jika output berbeda, ada masalah yang perlu diperbaiki sesuai panduan di atas.

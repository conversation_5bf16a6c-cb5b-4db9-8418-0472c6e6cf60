<?php
/**
 * Test page template for project filters
 * 
 * This is a sample template to test the project filter functionality.
 * You can use this as a reference for implementing the filters on your actual page.
 * 
 * To use this template:
 * 1. Create a new page in WordPress admin
 * 2. Set the page template to this file (if using as a page template)
 * 3. Or copy the content structure to your existing page
 */

get_header(); ?>

<div class="container">
    <div class="content-area">
        <main class="site-main">
            
            <h1>Projects</h1>
            
            <!-- Filter Controls -->
            <?php echo do_shortcode('[project_filters target_query=".projects-query"]'); ?>
            
            <!-- Projects Query Loop Container -->
            <div class="projects-query">
                <div class="gb-element-82ae69af">
                    <div class="general-border general-box-shadow gb-element-39bff0e1"></div>
                    <div class="gb-looper-f431c559" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem;">
                        
                        <?php
                        // Sample query for testing - replace with your actual Query Loop block
                        $projects_query = new WP_Query(array(
                            'post_type' => 'projects',
                            'posts_per_page' => 12,
                            'post_status' => 'publish',
                            'paged' => get_query_var('paged') ? get_query_var('paged') : 1
                        ));
                        
                        if ($projects_query->have_posts()) :
                            while ($projects_query->have_posts()) : $projects_query->the_post();
                                
                                // Get taxonomy terms
                                $year_terms = get_the_terms(get_the_ID(), 'project-year');
                                $industry_terms = get_the_terms(get_the_ID(), 'industry-category');
                                
                                // Build CSS classes
                                $css_classes = array('gb-loop-item', 'general-box-shadow', 'general-border');
                                
                                if ($year_terms && !is_wp_error($year_terms)) {
                                    foreach ($year_terms as $term) {
                                        $css_classes[] = 'project-year-' . $term->slug;
                                    }
                                }
                                
                                if ($industry_terms && !is_wp_error($industry_terms)) {
                                    foreach ($industry_terms as $term) {
                                        $css_classes[] = 'industry-' . $term->slug;
                                    }
                                }
                                
                                // Get ACF fields
                                $end_user = get_field('end-user');
                                $location = get_field('location');
                                $scope = get_field('scope');
                                $technologies = get_field('technologies');
                                
                                $year_display = '';
                                if ($year_terms && !is_wp_error($year_terms)) {
                                    $year_display = $year_terms[0]->name;
                                }
                                ?>
                                
                                <div class="<?php echo esc_attr(implode(' ', $css_classes)); ?>">
                                    <div class="project-card">
                                        <h3 class="project-title"><?php the_title(); ?></h3>
                                        <div class="project-status">✓ Completed</div>
                                        
                                        <?php if ($end_user): ?>
                                            <div class="project-field">
                                                <strong>End User:</strong> <?php echo esc_html($end_user); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($location): ?>
                                            <div class="project-field">
                                                <strong>Location:</strong> <?php echo esc_html($location); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($year_display): ?>
                                            <div class="project-field">
                                                <strong>Year:</strong> <?php echo esc_html($year_display); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($scope && is_array($scope)): ?>
                                            <div class="project-field">
                                                <strong>Scope:</strong>
                                                <ul>
                                                    <?php foreach ($scope as $scope_item): ?>
                                                        <li><?php echo esc_html($scope_item); ?></li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="no-results">
                                <p>No projects found.</p>
                            </div>
                        <?php endif; ?>
                        
                        <?php wp_reset_postdata(); ?>
                        
                    </div>
                </div>
            </div>
            
            <!-- Pagination -->
            <?php
            if ($projects_query->max_num_pages > 1) {
                echo get_project_pagination_html(
                    max(1, get_query_var('paged')),
                    $projects_query->max_num_pages
                );
            }
            ?>
            
        </main>
    </div>
</div>

<?php get_footer(); ?>
